// Clupa奢华按钮组件
// 具备多层次反馈、闪烁确认、触觉反馈等高级特效

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import '../WeaveRoutes/clu_luxury_theme.dart';

enum CluButtonType {
  primary,    // 主要按钮
  secondary,  // 次要按钮
  tertiary,   // 第三级按钮
  danger,     // 危险操作按钮
}

enum CluButtonSize {
  small,
  medium,
  large,
}

class CluLuxuryButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final CluButtonType type;
  final CluButtonSize size;
  final IconData? icon;
  final bool isLoading;
  final bool isEnabled;
  final bool showShimmerOnPress;
  final double? width;

  const CluLuxuryButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = CluButtonType.primary,
    this.size = CluButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isEnabled = true,
    this.showShimmerOnPress = false,
    this.width,
  });

  @override
  State<CluLuxuryButton> createState() => _CluLuxuryButtonState();
}

class _CluLuxuryButtonState extends State<CluLuxuryButton>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _shimmerController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shimmerAnimation;
  
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _scaleController = AnimationController(
      duration: CluLuxuryTheme.quickDuration,
      vsync: this,
    );

    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: CluLuxuryTheme.iosEaseInOut,
    ));

    _shimmerAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _shimmerController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _shimmerController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (!_isEffectivelyEnabled) return;
    
    setState(() {
      _isPressed = true;
    });
    
    _scaleController.forward();
    HapticFeedback.lightImpact();
  }

  void _handleTapUp(TapUpDetails details) {
    _handleTapEnd();
  }

  void _handleTapCancel() {
    _handleTapEnd();
  }

  void _handleTapEnd() {
    if (!mounted) return;
    
    setState(() {
      _isPressed = false;
    });
    
    _scaleController.reverse();
  }

  void _handleTap() {
    if (!_isEffectivelyEnabled) return;

    // 重要操作显示闪烁效果
    if (widget.showShimmerOnPress) {
      _shimmerController.forward().then((_) {
        _shimmerController.reverse();
      });
      HapticFeedback.mediumImpact();
    } else {
      HapticFeedback.lightImpact();
    }

    widget.onPressed?.call();
  }

  bool get _isEffectivelyEnabled => 
      widget.isEnabled && !widget.isLoading && widget.onPressed != null;

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_scaleAnimation, _shimmerAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _handleTapDown,
            onTapUp: _handleTapUp,
            onTapCancel: _handleTapCancel,
            onTap: _handleTap,
            child: AnimatedContainer(
              duration: CluLuxuryTheme.standardDuration,
              curve: CluLuxuryTheme.iosEaseInOut,
              width: widget.width,
              height: _getButtonHeight(),
              decoration: _buildButtonDecoration(),
              child: _buildButtonContent(),
            ),
          ),
        );
      },
    );
  }

  double _getButtonHeight() {
    switch (widget.size) {
      case CluButtonSize.small:
        return 36;
      case CluButtonSize.medium:
        return 48;
      case CluButtonSize.large:
        return 56;
    }
  }

  BoxDecoration _buildButtonDecoration() {
    Color backgroundColor;
    List<BoxShadow> shadows = [];

    // 根据按钮类型设置颜色
    switch (widget.type) {
      case CluButtonType.primary:
        backgroundColor = _isEffectivelyEnabled 
            ? CluLuxuryTheme.warmTaupe 
            : CluLuxuryTheme.warmTaupe.withOpacity(0.3);
        break;
      case CluButtonType.secondary:
        backgroundColor = _isEffectivelyEnabled 
            ? CluLuxuryTheme.dustyRose 
            : CluLuxuryTheme.dustyRose.withOpacity(0.3);
        break;
      case CluButtonType.tertiary:
        backgroundColor = _isEffectivelyEnabled 
            ? CluLuxuryTheme.vintageWhite 
            : CluLuxuryTheme.vintageWhite.withOpacity(0.3);
        break;
      case CluButtonType.danger:
        backgroundColor = _isEffectivelyEnabled 
            ? CluLuxuryTheme.errorRed 
            : CluLuxuryTheme.errorRed.withOpacity(0.3);
        break;
    }

    // 按下状态调整颜色
    if (_isPressed && _isEffectivelyEnabled) {
      backgroundColor = backgroundColor.withOpacity(0.8);
    }

    // 添加阴影
    if (_isEffectivelyEnabled && !_isPressed) {
      shadows = CluLuxuryTheme.lightShadow;
    }

    // 闪烁效果
    if (_shimmerAnimation.value > 0) {
      shadows.add(BoxShadow(
        color: CluLuxuryTheme.shimmerGold.withOpacity(_shimmerAnimation.value * 0.6),
        blurRadius: 12,
        spreadRadius: 2,
      ));
    }

    return BoxDecoration(
      color: backgroundColor,
      borderRadius: BorderRadius.circular(CluLuxuryTheme.buttonRadius),
      boxShadow: shadows,
      border: widget.type == CluButtonType.tertiary
          ? Border.all(
              color: CluLuxuryTheme.warmTaupe.withOpacity(0.3),
              width: 1,
            )
          : null,
    );
  }

  Widget _buildButtonContent() {
    Color textColor;
    
    switch (widget.type) {
      case CluButtonType.primary:
      case CluButtonType.secondary:
      case CluButtonType.danger:
        textColor = Colors.white;
        break;
      case CluButtonType.tertiary:
        textColor = CluLuxuryTheme.deepDenim;
        break;
    }

    if (!_isEffectivelyEnabled) {
      textColor = textColor.withOpacity(0.5);
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: _getHorizontalPadding(),
        vertical: CluLuxuryTheme.spacing1,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (widget.isLoading) ...[
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(textColor),
              ),
            ),
            const SizedBox(width: CluLuxuryTheme.spacing1),
          ] else if (widget.icon != null) ...[
            Icon(
              widget.icon,
              size: _getIconSize(),
              color: textColor,
            ),
            const SizedBox(width: CluLuxuryTheme.spacing1),
          ],
          Flexible(
            child: Text(
              widget.text,
              style: TextStyle(
                fontSize: _getFontSize(),
                fontWeight: FontWeight.w600,
                color: textColor,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  double _getHorizontalPadding() {
    switch (widget.size) {
      case CluButtonSize.small:
        return CluLuxuryTheme.spacing2;
      case CluButtonSize.medium:
        return CluLuxuryTheme.spacing3;
      case CluButtonSize.large:
        return CluLuxuryTheme.spacing5;
    }
  }

  double _getFontSize() {
    switch (widget.size) {
      case CluButtonSize.small:
        return 13;
      case CluButtonSize.medium:
        return 15;
      case CluButtonSize.large:
        return 17;
    }
  }

  double _getIconSize() {
    switch (widget.size) {
      case CluButtonSize.small:
        return 16;
      case CluButtonSize.medium:
        return 18;
      case CluButtonSize.large:
        return 20;
    }
  }
}
