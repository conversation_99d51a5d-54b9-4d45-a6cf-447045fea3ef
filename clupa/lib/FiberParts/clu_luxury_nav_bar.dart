// Clupa奢华底部导航栏组件
// 具备闪烁反馈、缩放动画、毛玻璃背景等高级特效

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import '../WeaveRoutes/clu_luxury_theme.dart';

class CluLuxuryNavBar extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<CluNavBarItem> items;

  const CluLuxuryNavBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
  });

  @override
  State<CluLuxuryNavBar> createState() => _CluLuxuryNavBarState();
}

class _CluLuxuryNavBarState extends State<CluLuxuryNavBar>
    with TickerProviderStateMixin {
  late List<AnimationController> _scaleControllers;
  late List<AnimationController> _shimmerControllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<double>> _shimmerAnimations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _scaleControllers = List.generate(
      widget.items.length,
      (index) => AnimationController(
        duration: CluLuxuryTheme.quickDuration,
        vsync: this,
      ),
    );

    _shimmerControllers = List.generate(
      widget.items.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 1000),
        vsync: this,
      ),
    );

    _scaleAnimations = _scaleControllers.map((controller) {
      return Tween<double>(begin: 1.0, end: 1.2).animate(
        CurvedAnimation(
          parent: controller,
          curve: CluLuxuryTheme.iosEaseInOut,
        ),
      );
    }).toList();

    _shimmerAnimations = _shimmerControllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: controller,
          curve: Curves.easeInOut,
        ),
      );
    }).toList();
  }

  @override
  void dispose() {
    for (var controller in _scaleControllers) {
      controller.dispose();
    }
    for (var controller in _shimmerControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _onItemTapped(int index) {
    if (index == widget.currentIndex) return;

    // 触觉反馈
    HapticFeedback.lightImpact();

    // 缩放动画
    _scaleControllers[index].forward().then((_) {
      _scaleControllers[index].reverse();
    });

    // 闪烁动画
    _shimmerControllers[index].forward().then((_) {
      _shimmerControllers[index].reverse();
    });

    // 回调
    widget.onTap(index);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 88 + MediaQuery.of(context).padding.bottom,
      decoration: const BoxDecoration(
        color: Colors.transparent,
      ),
      child: ClipRRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: CluLuxuryTheme.blurSigma,
            sigmaY: CluLuxuryTheme.blurSigma,
          ),
          child: Container(
            decoration: BoxDecoration(
              color: CluLuxuryTheme.glassBackground,
              border: Border(
                top: BorderSide(
                  color: CluLuxuryTheme.warmTaupe.withOpacity(0.2),
                  width: 0.5,
                ),
              ),
            ),
            child: SafeArea(
              top: false,
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: CluLuxuryTheme.spacing2,
                  vertical: CluLuxuryTheme.spacing1,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: List.generate(widget.items.length, (index) {
                    return _buildNavItem(index);
                  }),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(int index) {
    final item = widget.items[index];
    final isSelected = index == widget.currentIndex;

    return Expanded(
      child: GestureDetector(
        onTap: () => _onItemTapped(index),
        behavior: HitTestBehavior.opaque,
        child: AnimatedBuilder(
          animation: Listenable.merge([
            _scaleAnimations[index],
            _shimmerAnimations[index],
          ]),
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimations[index].value,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: CluLuxuryTheme.spacing1,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 图标容器
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: isSelected
                            ? CluLuxuryTheme.warmTaupe.withOpacity(0.1)
                            : Colors.transparent,
                        // 闪烁效果
                        boxShadow: _shimmerAnimations[index].value > 0
                            ? [
                                BoxShadow(
                                  color: CluLuxuryTheme.shimmerGold
                                      .withOpacity(_shimmerAnimations[index].value * 0.6),
                                  blurRadius: 8,
                                  spreadRadius: 2,
                                ),
                              ]
                            : null,
                      ),
                      child: Icon(
                        isSelected ? item.selectedIcon : item.icon,
                        size: 24,
                        color: isSelected
                            ? CluLuxuryTheme.warmTaupe
                            : CluLuxuryTheme.oliveDrab,
                      ),
                    ),
                    const SizedBox(height: 4),
                    // 标签文字
                    Text(
                      item.label,
                      style: TextStyle(
                        fontSize: 11,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        color: isSelected
                            ? CluLuxuryTheme.warmTaupe
                            : CluLuxuryTheme.oliveDrab,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

// 导航栏项目数据类
class CluNavBarItem {
  final IconData icon;
  final IconData selectedIcon;
  final String label;

  const CluNavBarItem({
    required this.icon,
    required this.selectedIcon,
    required this.label,
  });
}

// 预定义的导航栏项目
class CluNavBarItems {
  static const List<CluNavBarItem> mainTabs = [
    CluNavBarItem(
      icon: CupertinoIcons.photo_on_rectangle,
      selectedIcon: CupertinoIcons.photo_fill_on_rectangle_fill,
      label: 'Gallery',
    ),
    CluNavBarItem(
      icon: CupertinoIcons.plus_circle,
      selectedIcon: CupertinoIcons.plus_circle_fill,
      label: 'Create',
    ),
    CluNavBarItem(
      icon: CupertinoIcons.sparkles,
      selectedIcon: CupertinoIcons.sparkles,
      label: 'AI Guide',
    ),
    CluNavBarItem(
      icon: CupertinoIcons.person_crop_circle,
      selectedIcon: CupertinoIcons.person_crop_circle_fill,
      label: 'Profile',
    ),
  ];
}
