// Clupa头像选择器组件
// 用于选择和更换用户头像

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../WeaveRoutes/clu_luxury_theme.dart';
import '../ThreadStore/clu_vip_config_manager.dart';

class CluAvatarPicker extends StatefulWidget {
  final String currentAvatar;
  final Function(String) onAvatarSelected;

  const CluAvatarPicker({
    super.key,
    required this.currentAvatar,
    required this.onAvatarSelected,
  });

  @override
  State<CluAvatarPicker> createState() => _CluAvatarPickerState();
}

class _CluAvatarPickerState extends State<CluAvatarPicker> {
  late String _selectedAvatar;

  @override
  void initState() {
    super.initState();
    _selectedAvatar = widget.currentAvatar;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 400,
      decoration: const BoxDecoration(
        color: CluLuxuryTheme.vintageWhite,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // 顶部标题栏
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: CluLuxuryTheme.warmTaupe.withOpacity(0.2),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text(
                    'Cancel',
                    style: TextStyle(
                      color: CluLuxuryTheme.oliveDrab,
                      fontSize: 16,
                      decoration: TextDecoration.none,
                    ),
                  ),
                ),
                const Text(
                  'Choose Avatar',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: CluLuxuryTheme.deepDenim,
                    decoration: TextDecoration.none,
                  ),
                ),
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: _saveSelection,
                  child: const Text(
                    'Done',
                    style: TextStyle(
                      color: CluLuxuryTheme.warmTaupe,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      decoration: TextDecoration.none,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 头像网格
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 1,
                ),
                itemCount: CluVipConfigManager.availableAvatars.length,
                itemBuilder: (context, index) {
                  final avatarPath = CluVipConfigManager.availableAvatars[index];
                  final isSelected = avatarPath == _selectedAvatar;

                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedAvatar = avatarPath;
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isSelected 
                              ? CluLuxuryTheme.warmTaupe 
                              : CluLuxuryTheme.warmTaupe.withOpacity(0.3),
                          width: isSelected ? 4 : 2,
                        ),
                        boxShadow: isSelected ? [
                          BoxShadow(
                            color: CluLuxuryTheme.warmTaupe.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ] : null,
                      ),
                      child: ClipOval(
                        child: Image.asset(
                          avatarPath,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: CluLuxuryTheme.warmTaupe.withOpacity(0.2),
                              child: const Icon(
                                CupertinoIcons.person_fill,
                                color: CluLuxuryTheme.warmTaupe,
                                size: 40,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          // 底部随机按钮
          Container(
            padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
            child: SizedBox(
              width: double.infinity,
              child: CupertinoButton(
                color: CluLuxuryTheme.dustyRose.withOpacity(0.8),
                borderRadius: BorderRadius.circular(12),
                onPressed: _randomizeAvatar,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Icon(
                      CupertinoIcons.shuffle,
                      color: Colors.white,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Text(
                      'Random Avatar',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _randomizeAvatar() {
    final availableAvatars = CluVipConfigManager.availableAvatars;
    final currentIndex = availableAvatars.indexOf(_selectedAvatar);
    
    // 选择一个不同的头像
    int newIndex;
    do {
      newIndex = (currentIndex + 1 + (DateTime.now().millisecondsSinceEpoch % (availableAvatars.length - 1))) % availableAvatars.length;
    } while (newIndex == currentIndex && availableAvatars.length > 1);
    
    setState(() {
      _selectedAvatar = availableAvatars[newIndex];
    });
  }

  void _saveSelection() {
    widget.onAvatarSelected(_selectedAvatar);
    Navigator.of(context).pop();
  }
}

// 显示头像选择器的便捷方法
Future<void> showAvatarPicker({
  required BuildContext context,
  required String currentAvatar,
  required Function(String) onAvatarSelected,
}) {
  return showCupertinoModalPopup(
    context: context,
    builder: (context) => CluAvatarPicker(
      currentAvatar: currentAvatar,
      onAvatarSelected: onAvatarSelected,
    ),
  );
}
