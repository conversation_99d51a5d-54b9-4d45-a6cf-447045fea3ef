// Clupa奢华卡片组件
// 具备悬浮效果、交互动画、内容过渡、边框闪烁等高级特效

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../WeaveRoutes/clu_luxury_theme.dart';

enum CluCardType {
  elevated,    // 悬浮卡片
  flat,        // 平面卡片
  outlined,    // 边框卡片
}

class CluLuxuryCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final CluCardType type;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;
  final bool showShimmerBorder;
  final bool isLoading;
  final Color? backgroundColor;
  final double? borderRadius;

  const CluLuxuryCard({
    super.key,
    required this.child,
    this.onTap,
    this.type = CluCardType.elevated,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.showShimmerBorder = false,
    this.isLoading = false,
    this.backgroundColor,
    this.borderRadius,
  });

  @override
  State<CluLuxuryCard> createState() => _CluLuxuryCardState();
}

class _CluLuxuryCardState extends State<CluLuxuryCard>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _pressController;
  late AnimationController _shimmerController;
  late AnimationController _loadingController;
  
  late Animation<double> _elevationAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shimmerAnimation;
  late Animation<double> _loadingAnimation;
  
  bool _isHovered = false;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    
    if (widget.showShimmerBorder) {
      _startShimmerAnimation();
    }
    
    if (widget.isLoading) {
      _startLoadingAnimation();
    }
  }

  void _initializeAnimations() {
    _hoverController = AnimationController(
      duration: CluLuxuryTheme.standardDuration,
      vsync: this,
    );

    _pressController = AnimationController(
      duration: CluLuxuryTheme.quickDuration,
      vsync: this,
    );

    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _loadingController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _elevationAnimation = Tween<double>(
      begin: 0.0,
      end: 8.0,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: CluLuxuryTheme.iosEaseOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _pressController,
      curve: CluLuxuryTheme.iosEaseInOut,
    ));

    _shimmerAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _shimmerController,
      curve: Curves.easeInOut,
    ));

    _loadingAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingController,
      curve: Curves.easeInOut,
    ));
  }

  void _startShimmerAnimation() {
    _shimmerController.repeat(reverse: true);
  }

  void _startLoadingAnimation() {
    _loadingController.repeat(reverse: true);
  }

  @override
  void didUpdateWidget(CluLuxuryCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.showShimmerBorder != oldWidget.showShimmerBorder) {
      if (widget.showShimmerBorder) {
        _startShimmerAnimation();
      } else {
        _shimmerController.stop();
        _shimmerController.reset();
      }
    }
    
    if (widget.isLoading != oldWidget.isLoading) {
      if (widget.isLoading) {
        _startLoadingAnimation();
      } else {
        _loadingController.stop();
        _loadingController.reset();
      }
    }
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _pressController.dispose();
    _shimmerController.dispose();
    _loadingController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.onTap == null) return;
    
    setState(() {
      _isPressed = true;
    });
    
    _pressController.forward();
    HapticFeedback.lightImpact();
  }

  void _handleTapUp(TapUpDetails details) {
    _handleTapEnd();
  }

  void _handleTapCancel() {
    _handleTapEnd();
  }

  void _handleTapEnd() {
    if (!mounted) return;
    
    setState(() {
      _isPressed = false;
    });
    
    _pressController.reverse();
  }

  void _handleHoverEnter(PointerEnterEvent event) {
    setState(() {
      _isHovered = true;
    });
    _hoverController.forward();
  }

  void _handleHoverExit(PointerExitEvent event) {
    setState(() {
      _isHovered = false;
    });
    _hoverController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _elevationAnimation,
        _scaleAnimation,
        _shimmerAnimation,
        _loadingAnimation,
      ]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: widget.margin,
            width: widget.width,
            height: widget.height,
            child: MouseRegion(
              onEnter: _handleHoverEnter,
              onExit: _handleHoverExit,
              child: GestureDetector(
                onTapDown: _handleTapDown,
                onTapUp: _handleTapUp,
                onTapCancel: _handleTapCancel,
                onTap: widget.onTap,
                child: AnimatedContainer(
                  duration: CluLuxuryTheme.standardDuration,
                  curve: CluLuxuryTheme.iosEaseInOut,
                  decoration: _buildCardDecoration(),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(
                      widget.borderRadius ?? CluLuxuryTheme.cardRadius,
                    ),
                    child: _buildCardContent(),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  BoxDecoration _buildCardDecoration() {
    Color backgroundColor = widget.backgroundColor ?? CluLuxuryTheme.vintageWhite;
    List<BoxShadow> shadows = [];
    Border? border;

    // 根据卡片类型设置样式
    switch (widget.type) {
      case CluCardType.elevated:
        if (_isHovered || _isPressed) {
          shadows = CluLuxuryTheme.mediumShadow;
        } else {
          shadows = CluLuxuryTheme.lightShadow;
        }
        break;
      case CluCardType.flat:
        // 平面卡片无阴影
        break;
      case CluCardType.outlined:
        border = Border.all(
          color: CluLuxuryTheme.warmTaupe.withOpacity(0.3),
          width: 1,
        );
        break;
    }

    // 闪烁边框效果
    if (widget.showShimmerBorder && _shimmerAnimation.value > 0) {
      border = Border.all(
        color: CluLuxuryTheme.shimmerGold.withOpacity(_shimmerAnimation.value),
        width: 2,
      );
      shadows.add(BoxShadow(
        color: CluLuxuryTheme.shimmerGold.withOpacity(_shimmerAnimation.value * 0.3),
        blurRadius: 8,
        spreadRadius: 1,
      ));
    }

    // 加载状态透明度
    if (widget.isLoading) {
      backgroundColor = backgroundColor.withOpacity(_loadingAnimation.value);
    }

    return BoxDecoration(
      color: backgroundColor,
      borderRadius: BorderRadius.circular(
        widget.borderRadius ?? CluLuxuryTheme.cardRadius,
      ),
      boxShadow: shadows,
      border: border,
    );
  }

  Widget _buildCardContent() {
    Widget content = Container(
      padding: widget.padding ?? const EdgeInsets.all(CluLuxuryTheme.spacing2),
      child: widget.child,
    );

    // 加载状态覆盖层
    if (widget.isLoading) {
      content = Stack(
        children: [
          content,
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                color: CluLuxuryTheme.vintageWhite.withOpacity(0.8),
                borderRadius: BorderRadius.circular(
                  widget.borderRadius ?? CluLuxuryTheme.cardRadius,
                ),
              ),
              child: const Center(
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      CluLuxuryTheme.warmTaupe,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      );
    }

    return content;
  }
}
