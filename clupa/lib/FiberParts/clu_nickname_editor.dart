// Clupa昵称编辑器组件
// 用于编辑和更换用户昵称

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../WeaveRoutes/clu_luxury_theme.dart';
import '../ThreadStore/clu_vip_config_manager.dart';

class CluNicknameEditor extends StatefulWidget {
  final String currentNickname;
  final Function(String) onNicknameChanged;

  const CluNicknameEditor({
    super.key,
    required this.currentNickname,
    required this.onNicknameChanged,
  });

  @override
  State<CluNicknameEditor> createState() => _CluNicknameEditorState();
}

class _CluNicknameEditorState extends State<CluNicknameEditor> {
  late TextEditingController _controller;
  late String _originalNickname;

  @override
  void initState() {
    super.initState();
    _originalNickname = widget.currentNickname;
    _controller = TextEditingController(text: widget.currentNickname);

    // 监听文本变化以更新字符计数
    _controller.addListener(() {
      setState(() {
        // 触发重建以更新字符计数显示
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.9,
          minHeight: 380,
        ),
        decoration: const BoxDecoration(
          color: CluLuxuryTheme.vintageWhite,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
          // 顶部标题栏
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: CluLuxuryTheme.warmTaupe.withOpacity(0.2),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text(
                    'Cancel',
                    style: TextStyle(
                      color: CluLuxuryTheme.oliveDrab,
                      fontSize: 16,
                      decoration: TextDecoration.none,
                    ),
                  ),
                ),
                const Text(
                  'Edit Nickname',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: CluLuxuryTheme.deepDenim,
                    decoration: TextDecoration.none,
                  ),
                ),
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: _saveNickname,
                  child: const Text(
                    'Save',
                    style: TextStyle(
                      color: CluLuxuryTheme.warmTaupe,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      decoration: TextDecoration.none,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 输入区域
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Nickname',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: CluLuxuryTheme.deepDenim,
                      decoration: TextDecoration.none,
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // 输入框
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: CluLuxuryTheme.warmTaupe.withOpacity(0.3),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: TextField(
                      controller: _controller,
                      maxLength: 20,
                      decoration: const InputDecoration(
                        hintText: 'Enter your nickname',
                        hintStyle: TextStyle(
                          color: CluLuxuryTheme.oliveDrab,
                          fontSize: 16,
                        ),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.all(16),
                        counterText: '',
                      ),
                      style: const TextStyle(
                        fontSize: 16,
                        color: CluLuxuryTheme.deepDenim,
                      ),
                      autofocus: true,
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  Text(
                    '${_controller.text.length}/20 characters',
                    style: TextStyle(
                      fontSize: 12,
                      color: CluLuxuryTheme.oliveDrab.withOpacity(0.7),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 随机昵称按钮
                  SizedBox(
                    width: double.infinity,
                    child: CupertinoButton(
                      color: CluLuxuryTheme.dustyRose.withOpacity(0.8),
                      borderRadius: BorderRadius.circular(12),
                      onPressed: _generateRandomNickname,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: const [
                          Icon(
                            CupertinoIcons.shuffle,
                            color: Colors.white,
                            size: 20,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Random Name',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    ),
    );
  }

  void _generateRandomNickname() {
    final randomNicknames = CluVipConfigManager.randomNicknames;
    String newNickname;
    
    // 确保不选择当前昵称
    do {
      final randomIndex = DateTime.now().millisecondsSinceEpoch % randomNicknames.length;
      newNickname = randomNicknames[randomIndex];
    } while (newNickname == _controller.text && randomNicknames.length > 1);
    
    setState(() {
      _controller.text = newNickname;
    });
  }

  void _saveNickname() {
    final newNickname = _controller.text.trim();
    
    if (newNickname.isEmpty) {
      _showError('Nickname cannot be empty');
      return;
    }
    
    if (newNickname.length > 20) {
      _showError('Nickname is too long');
      return;
    }
    
    widget.onNicknameChanged(newNickname);
    Navigator.of(context).pop();
  }

  void _showError(String message) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

// 显示昵称编辑器的便捷方法
Future<void> showNicknameEditor({
  required BuildContext context,
  required String currentNickname,
  required Function(String) onNicknameChanged,
}) {
  return showCupertinoModalPopup(
    context: context,
    builder: (context) => Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: CluNicknameEditor(
        currentNickname: currentNickname,
        onNicknameChanged: onNicknameChanged,
      ),
    ),
  );
}
