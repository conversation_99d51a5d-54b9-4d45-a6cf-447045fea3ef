// Clupa Redux Reducer - WeaveReducer
// 响应操作更新拼布状态，使用拼布主题命名

import 'package:redux/redux.dart';
import '../ThreadStore/clu_app_state.dart';
import '../ClothVault/clu_craft_piece_model.dart';
import '../ClothVault/clu_textile_folio_model.dart';
import 'clu_needle_actions.dart';

// 主Reducer，组合所有子Reducer
CluAppState cluWeaveReducer(CluAppState state, dynamic action) {
  return CluAppState(
    // VIP用户状态
    currentVip: _vipReducer(state.currentVip, action),
    isVipAuthenticated: _vipAuthReducer(state.isVipAuthenticated, action),
    vipAuthToken: _vipTokenReducer(state.vipAuthToken, action),
    
    // 作品状态
    craftGallery: _craftGalleryReducer(state.craftGallery, action),
    vipCraftHistory: _vipCraftHistoryReducer(state.vipCraftHistory, action),
    currentCraftDraft: _craftDraftReducer(state.currentCraftDraft, action),

    // 布料状态
    availableTextiles: _textilesReducer(state.availableTextiles, action),
    selectedTextile: _selectedTextileReducer(state.selectedTextile, action),
    
    // AI助手状态
    isPatternSageActive: _sageActiveReducer(state.isPatternSageActive, action),
    sageConversationHistory: _sageHistoryReducer(state.sageConversationHistory, action),
    isSageThinking: _sageThinkingReducer(state.isSageThinking, action),
    
    // 社区状态
    isPatchSquareLoading: _communityLoadingReducer(state.isPatchSquareLoading, action),
    communityFeed: _communityFeedReducer(state.communityFeed, action),
    currentFeedPage: _feedPageReducer(state.currentFeedPage, action),
    
    // UI状态
    selectedTabIndex: _tabIndexReducer(state.selectedTabIndex, action),
    isAppLoading: _appLoadingReducer(state.isAppLoading, action),
    errorMessage: _errorReducer(state.errorMessage, action),
    isDarkMode: _darkModeReducer(state.isDarkMode, action),
    
    // 内购状态
    remainingPublishCount: _publishCountReducer(state.remainingPublishCount, action),
    hasUnlimitedPublish: _unlimitedPublishReducer(state.hasUnlimitedPublish, action),
    purchasedFeatures: _purchasedFeaturesReducer(state.purchasedFeatures, action),
  );
}

// ============================================================================
// VIP用户相关Reducers
// ============================================================================

final _vipReducer = combineReducers([
  TypedReducer<dynamic, CluVipLoginSuccessAction>(_onVipLoginSuccess),
  TypedReducer<dynamic, CluVipLogoutAction>(_onVipLogout),
  TypedReducer<dynamic, CluVipUpdateProfileAction>(_onVipUpdateProfile),
]);

_onVipLoginSuccess(state, CluVipLoginSuccessAction action) {
  return action.vipProfile;
}

_onVipLogout(state, CluVipLogoutAction action) {
  return null;
}

_onVipUpdateProfile(state, CluVipUpdateProfileAction action) {
  return action.updatedProfile;
}

final _vipAuthReducer = combineReducers<bool>([
  TypedReducer<bool, CluVipLoginSuccessAction>(_onAuthSuccess),
  TypedReducer<bool, CluVipLogoutAction>(_onAuthLogout),
]);

bool _onAuthSuccess(bool state, CluVipLoginSuccessAction action) => true;
bool _onAuthLogout(bool state, CluVipLogoutAction action) => false;

final _vipTokenReducer = combineReducers([
  TypedReducer<dynamic, CluVipLoginSuccessAction>(_onTokenReceived),
  TypedReducer<dynamic, CluVipLogoutAction>(_onTokenCleared),
]);

_onTokenReceived(state, CluVipLoginSuccessAction action) => action.authToken;
_onTokenCleared(state, CluVipLogoutAction action) => null;

// ============================================================================
// 作品相关Reducers
// ============================================================================

final _craftGalleryReducer = combineReducers<List<CluCraftPiece>>([
  TypedReducer<List<CluCraftPiece>, CluLoadCraftGallerySuccessAction>(_onCraftGalleryLoaded),
  TypedReducer<List<CluCraftPiece>, CluPublishCraftSuccessAction>(_onCraftPublished),
  TypedReducer<List<CluCraftPiece>, CluLikeCraftAction>(_onCraftLiked),
  TypedReducer<List<CluCraftPiece>, CluUnlikeCraftAction>(_onCraftUnliked),
]);

List<CluCraftPiece> _onCraftGalleryLoaded(List<CluCraftPiece> state, CluLoadCraftGallerySuccessAction action) {
  return action.craftPieces;
}

List<CluCraftPiece> _onCraftPublished(List<CluCraftPiece> state, CluPublishCraftSuccessAction action) {
  return [action.publishedCraft, ...state];
}

List<CluCraftPiece> _onCraftLiked(List<CluCraftPiece> state, CluLikeCraftAction action) {
  return state.map((craft) {
    if (craft.craftId == action.craftId) {
      return craft.copyWith(
        isLikedByCurrentVip: true,
        likesCount: craft.likesCount + 1,
      );
    }
    return craft;
  }).toList();
}

List<CluCraftPiece> _onCraftUnliked(List<CluCraftPiece> state, CluUnlikeCraftAction action) {
  return state.map((craft) {
    if (craft.craftId == action.craftId) {
      return craft.copyWith(
        isLikedByCurrentVip: false,
        likesCount: craft.likesCount - 1,
      );
    }
    return craft;
  }).toList();
}

final _vipCraftHistoryReducer = combineReducers<List<CluCraftPiece>>([
  TypedReducer<List<CluCraftPiece>, CluPublishCraftSuccessAction>(_onVipCraftAdded),
]);

List<CluCraftPiece> _onVipCraftAdded(List<CluCraftPiece> state, CluPublishCraftSuccessAction action) {
  return [action.publishedCraft, ...state];
}

final _craftDraftReducer = combineReducers([
  TypedReducer<dynamic, CluCreateCraftDraftAction>(_onDraftCreated),
  TypedReducer<dynamic, CluUpdateCraftDraftAction>(_onDraftUpdated),
  TypedReducer<dynamic, CluPublishCraftSuccessAction>(_onDraftPublished),
]);

_onDraftCreated(state, CluCreateCraftDraftAction action) => action.craftDraft;
_onDraftUpdated(state, CluUpdateCraftDraftAction action) => action.updatedDraft;
_onDraftPublished(state, CluPublishCraftSuccessAction action) => null;

// ============================================================================
// 布料相关Reducers
// ============================================================================

final _textilesReducer = combineReducers<List<CluTextileFolio>>([
  TypedReducer<List<CluTextileFolio>, CluLoadTextileFoliosSuccessAction>(_onTextilesLoaded),
  TypedReducer<List<CluTextileFolio>, CluAddTextileFolioAction>(_onTextileAdded),
]);

List<CluTextileFolio> _onTextilesLoaded(List<CluTextileFolio> state, CluLoadTextileFoliosSuccessAction action) {
  return action.textileFolios;
}

List<CluTextileFolio> _onTextileAdded(List<CluTextileFolio> state, CluAddTextileFolioAction action) {
  return [...state, action.newTextile];
}

final _selectedTextileReducer = combineReducers([
  TypedReducer<dynamic, CluSelectTextileAction>(_onTextileSelected),
]);

_onTextileSelected(state, CluSelectTextileAction action) => action.selectedTextile;

// ============================================================================
// AI助手相关Reducers
// ============================================================================

final _sageActiveReducer = combineReducers<bool>([
  TypedReducer<bool, CluActivatePatternSageAction>(_onSageActivated),
  TypedReducer<bool, CluDeactivatePatternSageAction>(_onSageDeactivated),
]);

bool _onSageActivated(bool state, CluActivatePatternSageAction action) => true;
bool _onSageDeactivated(bool state, CluDeactivatePatternSageAction action) => false;

final _sageHistoryReducer = combineReducers<List<String>>([
  TypedReducer<List<String>, CluSendSageMessageAction>(_onSageMessageSent),
  TypedReducer<List<String>, CluReceiveSageResponseAction>(_onSageResponseReceived),
  TypedReducer<List<String>, CluClearSageHistoryAction>(_onSageHistoryCleared),
]);

List<String> _onSageMessageSent(List<String> state, CluSendSageMessageAction action) {
  return [...state, 'User: ${action.message}'];
}

List<String> _onSageResponseReceived(List<String> state, CluReceiveSageResponseAction action) {
  return [...state, 'Sage: ${action.response}'];
}

List<String> _onSageHistoryCleared(List<String> state, CluClearSageHistoryAction action) {
  return [];
}

final _sageThinkingReducer = combineReducers<bool>([
  TypedReducer<bool, CluSageThinkingAction>(_onSageThinkingChanged),
]);

bool _onSageThinkingChanged(bool state, CluSageThinkingAction action) => action.isThinking;

// ============================================================================
// 社区相关Reducers
// ============================================================================

final _communityLoadingReducer = combineReducers<bool>([
  TypedReducer<bool, CluLoadCommunityFeedAction>(_onCommunityLoadingStarted),
  TypedReducer<bool, CluLoadCommunityFeedSuccessAction>(_onCommunityLoadingFinished),
  TypedReducer<bool, CluLoadCommunityFeedFailureAction>(_onCommunityLoadingFinished),
]);

bool _onCommunityLoadingStarted(bool state, CluLoadCommunityFeedAction action) => true;
bool _onCommunityLoadingFinished(bool state, dynamic action) => false;

final _communityFeedReducer = combineReducers<List<CluCraftPiece>>([
  TypedReducer<List<CluCraftPiece>, CluLoadCommunityFeedSuccessAction>(_onCommunityFeedLoaded),
  TypedReducer<List<CluCraftPiece>, CluRefreshCommunityFeedAction>(_onCommunityFeedRefreshed),
]);

List<CluCraftPiece> _onCommunityFeedLoaded(List<CluCraftPiece> state, CluLoadCommunityFeedSuccessAction action) {
  if (action.page == 0) {
    return action.feedItems;
  } else {
    return [...state, ...action.feedItems];
  }
}

List<CluCraftPiece> _onCommunityFeedRefreshed(List<CluCraftPiece> state, CluRefreshCommunityFeedAction action) {
  return [];
}

final _feedPageReducer = combineReducers<int>([
  TypedReducer<int, CluLoadCommunityFeedSuccessAction>(_onFeedPageUpdated),
  TypedReducer<int, CluRefreshCommunityFeedAction>(_onFeedPageReset),
]);

int _onFeedPageUpdated(int state, CluLoadCommunityFeedSuccessAction action) => action.page;
int _onFeedPageReset(int state, CluRefreshCommunityFeedAction action) => 0;

// ============================================================================
// UI状态相关Reducers
// ============================================================================

final _tabIndexReducer = combineReducers<int>([
  TypedReducer<int, CluChangeTabAction>(_onTabChanged),
]);

int _onTabChanged(int state, CluChangeTabAction action) => action.tabIndex;

final _appLoadingReducer = combineReducers<bool>([
  TypedReducer<bool, CluSetLoadingAction>(_onLoadingChanged),
]);

bool _onLoadingChanged(bool state, CluSetLoadingAction action) => action.isLoading;

final _errorReducer = combineReducers([
  TypedReducer<dynamic, CluSetErrorAction>(_onErrorSet),
  TypedReducer<dynamic, CluClearErrorAction>(_onErrorCleared),
]);

_onErrorSet(state, CluSetErrorAction action) => action.errorMessage;
_onErrorCleared(state, CluClearErrorAction action) => null;

final _darkModeReducer = combineReducers<bool>([
  TypedReducer<bool, CluToggleDarkModeAction>(_onDarkModeToggled),
]);

bool _onDarkModeToggled(bool state, CluToggleDarkModeAction action) => !state;

// ============================================================================
// 内购相关Reducers
// ============================================================================

final _publishCountReducer = combineReducers<int>([
  TypedReducer<int, CluPublishCraftSuccessAction>(_onPublishCountDecreased),
  TypedReducer<int, CluPurchasePublishPackAction>(_onPublishCountIncreased),
]);

int _onPublishCountDecreased(int state, CluPublishCraftSuccessAction action) {
  return state > 0 ? state - 1 : 0;
}

int _onPublishCountIncreased(int state, CluPurchasePublishPackAction action) {
  return state + action.additionalPublishCount;
}

final _unlimitedPublishReducer = combineReducers<bool>([
  TypedReducer<bool, CluPurchaseSuccessAction>(_onUnlimitedPurchased),
]);

bool _onUnlimitedPurchased(bool state, CluPurchaseSuccessAction action) {
  return action.featureId == 'unlimited_publish' ? true : state;
}

final _purchasedFeaturesReducer = combineReducers<List<String>>([
  TypedReducer<List<String>, CluPurchaseSuccessAction>(_onFeaturePurchased),
]);

List<String> _onFeaturePurchased(List<String> state, CluPurchaseSuccessAction action) {
  return [...state, action.featureId];
}
