// Clupa Redux Actions - NeedleActions
// 定义所有应用操作，使用拼布主题命名

import '../ClothVault/clu_vip_model.dart';
import '../ClothVault/clu_craft_piece_model.dart';
import '../ClothVault/clu_textile_folio_model.dart';

// 基础Action类
abstract class CluNeedleAction {}

// ============================================================================
// VIP用户相关Actions
// ============================================================================

class CluVipLoginAction extends CluNeedleAction {
  final String email;
  final String password;
  
  CluVipLoginAction({required this.email, required this.password});
}

class CluVipLoginSuccessAction extends CluNeedleAction {
  final CluVipProfile vipProfile;
  final String authToken;
  
  CluVipLoginSuccessAction({required this.vipProfile, required this.authToken});
}

class CluVipLoginFailureAction extends CluNeedleAction {
  final String errorMessage;
  
  CluVipLoginFailureAction({required this.errorMessage});
}

class CluVipLogoutAction extends CluNeedleAction {}

class CluVipUpdateProfileAction extends CluNeedleAction {
  final CluVipProfile updatedProfile;
  
  CluVipUpdateProfileAction({required this.updatedProfile});
}

// ============================================================================
// 作品相关Actions
// ============================================================================

class CluLoadCraftGalleryAction extends CluNeedleAction {}

class CluLoadCraftGallerySuccessAction extends CluNeedleAction {
  final List<CluCraftPiece> craftPieces;
  
  CluLoadCraftGallerySuccessAction({required this.craftPieces});
}

class CluLoadCraftGalleryFailureAction extends CluNeedleAction {
  final String errorMessage;
  
  CluLoadCraftGalleryFailureAction({required this.errorMessage});
}

class CluCreateCraftDraftAction extends CluNeedleAction {
  final CluCraftPiece craftDraft;
  
  CluCreateCraftDraftAction({required this.craftDraft});
}

class CluUpdateCraftDraftAction extends CluNeedleAction {
  final CluCraftPiece updatedDraft;
  
  CluUpdateCraftDraftAction({required this.updatedDraft});
}

class CluPublishCraftAction extends CluNeedleAction {
  final CluCraftPiece craftToPublish;
  
  CluPublishCraftAction({required this.craftToPublish});
}

class CluPublishCraftSuccessAction extends CluNeedleAction {
  final CluCraftPiece publishedCraft;
  
  CluPublishCraftSuccessAction({required this.publishedCraft});
}

class CluPublishCraftFailureAction extends CluNeedleAction {
  final String errorMessage;
  
  CluPublishCraftFailureAction({required this.errorMessage});
}

class CluLikeCraftAction extends CluNeedleAction {
  final String craftId;
  
  CluLikeCraftAction({required this.craftId});
}

class CluUnlikeCraftAction extends CluNeedleAction {
  final String craftId;
  
  CluUnlikeCraftAction({required this.craftId});
}

class CluBookmarkCraftAction extends CluNeedleAction {
  final String craftId;
  
  CluBookmarkCraftAction({required this.craftId});
}

class CluUnbookmarkCraftAction extends CluNeedleAction {
  final String craftId;
  
  CluUnbookmarkCraftAction({required this.craftId});
}

// ============================================================================
// 布料相关Actions
// ============================================================================

class CluLoadTextileFoliosAction extends CluNeedleAction {}

class CluLoadTextileFoliosSuccessAction extends CluNeedleAction {
  final List<CluTextileFolio> textileFolios;
  
  CluLoadTextileFoliosSuccessAction({required this.textileFolios});
}

class CluSelectTextileAction extends CluNeedleAction {
  final CluTextileFolio selectedTextile;
  
  CluSelectTextileAction({required this.selectedTextile});
}

class CluAddTextileFolioAction extends CluNeedleAction {
  final CluTextileFolio newTextile;
  
  CluAddTextileFolioAction({required this.newTextile});
}

// ============================================================================
// AI助手相关Actions (PatternSage)
// ============================================================================

class CluActivatePatternSageAction extends CluNeedleAction {}

class CluDeactivatePatternSageAction extends CluNeedleAction {}

class CluSendSageMessageAction extends CluNeedleAction {
  final String message;
  
  CluSendSageMessageAction({required this.message});
}

class CluReceiveSageResponseAction extends CluNeedleAction {
  final String response;
  
  CluReceiveSageResponseAction({required this.response});
}

class CluSageThinkingAction extends CluNeedleAction {
  final bool isThinking;
  
  CluSageThinkingAction({required this.isThinking});
}

class CluClearSageHistoryAction extends CluNeedleAction {}

// ============================================================================
// 社区广场相关Actions (PatchSquare)
// ============================================================================

class CluLoadCommunityFeedAction extends CluNeedleAction {
  final int page;
  
  CluLoadCommunityFeedAction({this.page = 0});
}

class CluLoadCommunityFeedSuccessAction extends CluNeedleAction {
  final List<CluCraftPiece> feedItems;
  final int page;
  
  CluLoadCommunityFeedSuccessAction({required this.feedItems, required this.page});
}

class CluLoadCommunityFeedFailureAction extends CluNeedleAction {
  final String errorMessage;
  
  CluLoadCommunityFeedFailureAction({required this.errorMessage});
}

class CluRefreshCommunityFeedAction extends CluNeedleAction {}

// ============================================================================
// 导航相关Actions
// ============================================================================

class CluChangeTabAction extends CluNeedleAction {
  final int tabIndex;
  
  CluChangeTabAction({required this.tabIndex});
}

// ============================================================================
// 应用状态相关Actions
// ============================================================================

class CluSetLoadingAction extends CluNeedleAction {
  final bool isLoading;
  
  CluSetLoadingAction({required this.isLoading});
}

class CluSetErrorAction extends CluNeedleAction {
  final String? errorMessage;
  
  CluSetErrorAction({this.errorMessage});
}

class CluClearErrorAction extends CluNeedleAction {}

class CluToggleDarkModeAction extends CluNeedleAction {}

// ============================================================================
// 内购相关Actions
// ============================================================================

class CluPurchasePublishPackAction extends CluNeedleAction {
  final int additionalPublishCount;
  
  CluPurchasePublishPackAction({required this.additionalPublishCount});
}

class CluPurchaseSuccessAction extends CluNeedleAction {
  final String featureId;
  
  CluPurchaseSuccessAction({required this.featureId});
}

class CluPurchaseFailureAction extends CluNeedleAction {
  final String errorMessage;
  
  CluPurchaseFailureAction({required this.errorMessage});
}
