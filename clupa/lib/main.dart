// Clupa主应用入口
// 集成Redux状态管理和奢华iOS主题

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:redux/redux.dart';
import 'package:flutter_redux/flutter_redux.dart';

import 'ThreadStore/clu_app_state.dart';
import 'StitchMaster/clu_weave_reducer.dart';
import 'WeaveRoutes/clu_luxury_theme.dart';
import 'LoomView/clu_main_hub_view.dart';

void main() {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 设置系统UI样式
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarStyle: StatusBarStyle.dark,
      statusBarBrightness: Brightness.light,
      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // 创建Redux Store
  final store = Store<CluAppState>(
    cluWeaveReducer,
    initialState: CluAppState.initial(),
  );

  runApp(ClupaApp(store: store));
}

class ClupaApp extends StatelessWidget {
  final Store<CluAppState> store;

  const ClupaApp({super.key, required this.store});

  @override
  Widget build(BuildContext context) {
    return StoreProvider<CluAppState>(
      store: store,
      child: StoreConnector<CluAppState, bool>(
        converter: (store) => store.state.isDarkMode,
        builder: (context, isDarkMode) {
          return MaterialApp(
            title: 'Clupa',
            debugShowCheckedModeBanner: false,

            // 应用主题
            theme: CluLuxuryTheme.lightTheme,
            darkTheme: CluLuxuryTheme.darkTheme,
            themeMode: isDarkMode ? ThemeMode.dark : ThemeMode.light,

            // 本地化配置
            locale: const Locale('en', 'US'),

            // 主页面
            home: const CluMainHubView(),

            // 路由配置
            onGenerateRoute: _generateRoute,
          );
        },
      ),
    );
  }

  Route<dynamic>? _generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case '/':
        return CupertinoPageRoute(
          builder: (_) => const CluMainHubView(),
          settings: settings,
        );
      default:
        return CupertinoPageRoute(
          builder: (_) => const CluMainHubView(),
          settings: settings,
        );
    }
  }
}
