// Clupa应用全局状态定义
// 使用Redux状态管理，遵循拼布主题命名规则

import 'package:meta/meta.dart';
import '../ClothVault/clu_vip_model.dart';
import '../ClothVault/clu_craft_piece_model.dart';
import '../ClothVault/clu_textile_folio_model.dart';

@immutable
class CluAppState {
  // 用户状态流 - VipProfile相关状态
  final CluVipProfile? currentVip;
  final bool isVipAuthenticated;
  final String? vipAuthToken;
  
  // 拼布状态库 - 创作相关状态
  final List<CluCraftPiece> craftGallery;
  final List<CluCraftPiece> vipCraftHistory;
  final CluCraftPiece? currentCraftDraft;
  
  // 布料状态流 - 材质和设计状态
  final List<CluTextileFolio> availableTextiles;
  final CluTextileFolio? selectedTextile;
  
  // AI助手状态
  final bool isPatternSageActive;
  final List<String> sageConversationHistory;
  final bool isSageThinking;
  
  // 社区广场状态
  final bool isPatchSquareLoading;
  final List<CluCraftPiece> communityFeed;
  final int currentFeedPage;
  
  // 应用UI状态
  final int selectedTabIndex;
  final bool isAppLoading;
  final String? errorMessage;
  final bool isDarkMode;
  
  // 内购状态
  final int remainingPublishCount;
  final bool hasUnlimitedPublish;
  final List<String> purchasedFeatures;

  const CluAppState({
    this.currentVip,
    this.isVipAuthenticated = false,
    this.vipAuthToken,
    this.craftGallery = const [],
    this.vipCraftHistory = const [],
    this.currentCraftDraft,
    this.availableTextiles = const [],
    this.selectedTextile,
    this.isPatternSageActive = false,
    this.sageConversationHistory = const [],
    this.isSageThinking = false,
    this.isPatchSquareLoading = false,
    this.communityFeed = const [],
    this.currentFeedPage = 0,
    this.selectedTabIndex = 0,
    this.isAppLoading = false,
    this.errorMessage,
    this.isDarkMode = false,
    this.remainingPublishCount = 3,
    this.hasUnlimitedPublish = false,
    this.purchasedFeatures = const [],
  });

  // 初始状态工厂方法
  factory CluAppState.initial() {
    return const CluAppState(
      isVipAuthenticated: false,
      craftGallery: [],
      vipCraftHistory: [],
      availableTextiles: [],
      sageConversationHistory: [],
      communityFeed: [],
      selectedTabIndex: 0,
      isAppLoading: false,
      isDarkMode: false,
      remainingPublishCount: 3,
      hasUnlimitedPublish: false,
      purchasedFeatures: [],
    );
  }

  // 状态复制方法，用于创建新状态
  CluAppState copyWith({
    CluVipProfile? currentVip,
    bool? isVipAuthenticated,
    String? vipAuthToken,
    List<CluCraftPiece>? craftGallery,
    List<CluCraftPiece>? vipCraftHistory,
    CluCraftPiece? currentCraftDraft,
    List<CluTextileFolio>? availableTextiles,
    CluTextileFolio? selectedTextile,
    bool? isPatternSageActive,
    List<String>? sageConversationHistory,
    bool? isSageThinking,
    bool? isPatchSquareLoading,
    List<CluCraftPiece>? communityFeed,
    int? currentFeedPage,
    int? selectedTabIndex,
    bool? isAppLoading,
    String? errorMessage,
    bool? isDarkMode,
    int? remainingPublishCount,
    bool? hasUnlimitedPublish,
    List<String>? purchasedFeatures,
  }) {
    return CluAppState(
      currentVip: currentVip ?? this.currentVip,
      isVipAuthenticated: isVipAuthenticated ?? this.isVipAuthenticated,
      vipAuthToken: vipAuthToken ?? this.vipAuthToken,
      craftGallery: craftGallery ?? this.craftGallery,
      vipCraftHistory: vipCraftHistory ?? this.vipCraftHistory,
      currentCraftDraft: currentCraftDraft ?? this.currentCraftDraft,
      availableTextiles: availableTextiles ?? this.availableTextiles,
      selectedTextile: selectedTextile ?? this.selectedTextile,
      isPatternSageActive: isPatternSageActive ?? this.isPatternSageActive,
      sageConversationHistory: sageConversationHistory ?? this.sageConversationHistory,
      isSageThinking: isSageThinking ?? this.isSageThinking,
      isPatchSquareLoading: isPatchSquareLoading ?? this.isPatchSquareLoading,
      communityFeed: communityFeed ?? this.communityFeed,
      currentFeedPage: currentFeedPage ?? this.currentFeedPage,
      selectedTabIndex: selectedTabIndex ?? this.selectedTabIndex,
      isAppLoading: isAppLoading ?? this.isAppLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      isDarkMode: isDarkMode ?? this.isDarkMode,
      remainingPublishCount: remainingPublishCount ?? this.remainingPublishCount,
      hasUnlimitedPublish: hasUnlimitedPublish ?? this.hasUnlimitedPublish,
      purchasedFeatures: purchasedFeatures ?? this.purchasedFeatures,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CluAppState &&
          runtimeType == other.runtimeType &&
          currentVip == other.currentVip &&
          isVipAuthenticated == other.isVipAuthenticated &&
          selectedTabIndex == other.selectedTabIndex &&
          isAppLoading == other.isAppLoading;

  @override
  int get hashCode =>
      currentVip.hashCode ^
      isVipAuthenticated.hashCode ^
      selectedTabIndex.hashCode ^
      isAppLoading.hashCode;

  @override
  String toString() {
    return 'CluAppState{vip: $currentVip, authenticated: $isVipAuthenticated, tab: $selectedTabIndex}';
  }
}
