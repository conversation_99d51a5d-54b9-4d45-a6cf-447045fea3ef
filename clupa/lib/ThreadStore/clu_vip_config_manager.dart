// Clupa用户配置管理器
// 管理用户头像、昵称等个人信息

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:math';

class CluVipConfigManager {
  static final CluVipConfigManager _instance = CluVipConfigManager._internal();
  static CluVipConfigManager get instance => _instance;
  CluVipConfigManager._internal();

  SharedPreferences? _prefs;
  
  // 用户配置信息
  String _currentAvatar = 'assets/avatars/clu_avatar_01.jpg';
  String _currentNickname = 'Guest User';
  
  // 可用头像列表
  static const List<String> availableAvatars = [
    'assets/avatars/clu_avatar_01.jpg',
    'assets/avatars/clu_avatar_02.jpg',
    'assets/avatars/clu_avatar_03.jpg',
    'assets/avatars/clu_avatar_04.jpg',
    'assets/avatars/clu_avatar_05.jpg',
    'assets/avatars/clu_avatar_06.jpg',
  ];

  // 随机昵称列表
  static const List<String> randomNicknames = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>',
    '<PERSON> <PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>',
    '<PERSON> <PERSON>', '<PERSON>', '<PERSON> <PERSON>', '<PERSON>', '<PERSON> <PERSON>',
    '<PERSON> <PERSON>', 'Layla Cao', 'Riley Xie', 'Zoey Hu', 'Nora Liang',
    'Lily Fang', 'Eleanor Tian', 'Hannah Wen', 'Lillian Yao', 'Addison Bai',
    'Aubrey Qiu', 'Ellie Meng', 'Stella Ding', 'Natalie Kang', 'Zoe Cheng',
    'Leah Shen', 'Hazel Jin', 'Violet Xia', 'Aurora Cui', 'Savannah Lv',
    'Audrey Nie', 'Brooklyn Ruan', 'Bella Jia', 'Claire Hao', 'Skylar Pei',
  ];

  // Getters
  String get currentAvatar => _currentAvatar;
  String get currentNickname => _currentNickname;

  // 初始化
  Future<void> initializeConfig() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadUserConfig();
  }

  // 加载用户配置
  Future<void> _loadUserConfig() async {
    if (_prefs == null) return;

    _currentAvatar = _prefs!.getString('user_avatar') ?? availableAvatars.first;
    _currentNickname = _prefs!.getString('user_nickname') ?? _generateRandomNickname();
    
    // 如果是第一次使用，生成随机昵称并保存
    if (!_prefs!.containsKey('user_nickname')) {
      await updateNickname(_currentNickname);
    }
  }

  // 生成随机昵称
  String _generateRandomNickname() {
    final random = Random();
    return randomNicknames[random.nextInt(randomNicknames.length)];
  }

  // 更新头像
  Future<bool> updateAvatar(String avatarPath) async {
    if (_prefs == null || !availableAvatars.contains(avatarPath)) {
      return false;
    }

    try {
      await _prefs!.setString('user_avatar', avatarPath);
      _currentAvatar = avatarPath;
      return true;
    } catch (e) {
      debugPrint('Failed to update avatar: $e');
      return false;
    }
  }

  // 更新昵称
  Future<bool> updateNickname(String nickname) async {
    if (_prefs == null || nickname.trim().isEmpty) {
      return false;
    }

    try {
      await _prefs!.setString('user_nickname', nickname.trim());
      _currentNickname = nickname.trim();
      return true;
    } catch (e) {
      debugPrint('Failed to update nickname: $e');
      return false;
    }
  }

  // 随机更换头像
  Future<bool> randomizeAvatar() async {
    final random = Random();
    String newAvatar;
    
    // 确保不选择当前头像
    do {
      newAvatar = availableAvatars[random.nextInt(availableAvatars.length)];
    } while (newAvatar == _currentAvatar && availableAvatars.length > 1);
    
    return await updateAvatar(newAvatar);
  }

  // 随机更换昵称
  Future<bool> randomizeNickname() async {
    final newNickname = _generateRandomNickname();
    return await updateNickname(newNickname);
  }

  // 重置为默认配置
  Future<bool> resetToDefault() async {
    if (_prefs == null) return false;

    try {
      await _prefs!.remove('user_avatar');
      await _prefs!.remove('user_nickname');
      
      _currentAvatar = availableAvatars.first;
      _currentNickname = _generateRandomNickname();
      
      await updateNickname(_currentNickname);
      return true;
    } catch (e) {
      debugPrint('Failed to reset config: $e');
      return false;
    }
  }

  // 获取头像索引
  int getCurrentAvatarIndex() {
    return availableAvatars.indexOf(_currentAvatar);
  }

  // 根据索引设置头像
  Future<bool> setAvatarByIndex(int index) async {
    if (index < 0 || index >= availableAvatars.length) {
      return false;
    }
    return await updateAvatar(availableAvatars[index]);
  }
}
