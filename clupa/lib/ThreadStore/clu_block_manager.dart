// Clupa屏蔽管理器 - BlockManager
// 管理屏蔽和举报的视频内容

import 'dart:convert';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

enum CluBlockReason {
  userBlocked,    // 用户主动屏蔽
  userReported,   // 用户举报
}

class CluBlockedItem {
  final String craftId;
  final String creatorId;
  final String creatorName;
  final String title;
  final CluBlockReason reason;
  final DateTime blockedAt;

  const CluBlockedItem({
    required this.craftId,
    required this.creatorId,
    required this.creatorName,
    required this.title,
    required this.reason,
    required this.blockedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'craft_id': craftId,
      'creator_id': creatorId,
      'creator_name': creatorName,
      'title': title,
      'reason': reason.name,
      'blocked_at': blockedAt.millisecondsSinceEpoch,
    };
  }

  factory CluBlockedItem.fromJson(Map<String, dynamic> json) {
    return CluBlockedItem(
      craftId: json['craft_id'] ?? '',
      creatorId: json['creator_id'] ?? '',
      creatorName: json['creator_name'] ?? '',
      title: json['title'] ?? '',
      reason: CluBlockReason.values.firstWhere(
        (e) => e.name == json['reason'],
        orElse: () => CluBlockReason.userBlocked,
      ),
      blockedAt: DateTime.fromMillisecondsSinceEpoch(json['blocked_at'] ?? 0),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CluBlockedItem && other.craftId == craftId;
  }

  @override
  int get hashCode => craftId.hashCode;
}

class CluBlockManager {
  static CluBlockManager? _instance;
  static CluBlockManager get instance => _instance ??= CluBlockManager._();
  
  CluBlockManager._();
  
  SharedPreferences? _prefs;
  final Set<String> _blockedCraftIds = {};
  final List<CluBlockedItem> _blockedItems = [];
  
  // 存储键
  static const String _blockedItemsKey = 'clu_blocked_items';
  
  // 流控制器
  final StreamController<List<CluBlockedItem>> _blockedItemsController = 
      StreamController<List<CluBlockedItem>>.broadcast();
  
  Stream<List<CluBlockedItem>> get blockedItemsStream => _blockedItemsController.stream;
  
  // 初始化
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadBlockedItems();
    } catch (e) {
      debugPrint('Failed to initialize block manager: $e');
    }
  }
  
  // 加载屏蔽项目
  Future<void> _loadBlockedItems() async {
    try {
      final itemsJson = _prefs?.getString(_blockedItemsKey);
      if (itemsJson != null) {
        final itemsList = jsonDecode(itemsJson) as List<dynamic>;
        _blockedItems.clear();
        _blockedCraftIds.clear();
        
        for (final itemData in itemsList) {
          final item = CluBlockedItem.fromJson(itemData);
          _blockedItems.add(item);
          _blockedCraftIds.add(item.craftId);
        }
        
        _blockedItemsController.add(List.from(_blockedItems));
      }
    } catch (e) {
      debugPrint('Failed to load blocked items: $e');
    }
  }
  
  // 保存屏蔽项目
  Future<void> _saveBlockedItems() async {
    try {
      final itemsList = _blockedItems.map((item) => item.toJson()).toList();
      final itemsJson = jsonEncode(itemsList);
      await _prefs?.setString(_blockedItemsKey, itemsJson);
      
      _blockedItemsController.add(List.from(_blockedItems));
    } catch (e) {
      debugPrint('Failed to save blocked items: $e');
    }
  }
  
  // 屏蔽视频
  Future<void> blockCraft({
    required String craftId,
    required String creatorId,
    required String creatorName,
    required String title,
    CluBlockReason reason = CluBlockReason.userBlocked,
  }) async {
    if (_blockedCraftIds.contains(craftId)) return;
    
    final blockedItem = CluBlockedItem(
      craftId: craftId,
      creatorId: creatorId,
      creatorName: creatorName,
      title: title,
      reason: reason,
      blockedAt: DateTime.now(),
    );
    
    _blockedItems.add(blockedItem);
    _blockedCraftIds.add(craftId);
    
    await _saveBlockedItems();
  }
  
  // 举报视频（同时屏蔽）
  Future<void> reportCraft({
    required String craftId,
    required String creatorId,
    required String creatorName,
    required String title,
  }) async {
    await blockCraft(
      craftId: craftId,
      creatorId: creatorId,
      creatorName: creatorName,
      title: title,
      reason: CluBlockReason.userReported,
    );
  }
  
  // 解除屏蔽
  Future<void> unblockCraft(String craftId) async {
    _blockedItems.removeWhere((item) => item.craftId == craftId);
    _blockedCraftIds.remove(craftId);
    
    await _saveBlockedItems();
  }
  
  // 检查是否被屏蔽
  bool isCraftBlocked(String craftId) {
    return _blockedCraftIds.contains(craftId);
  }
  
  // 获取所有屏蔽项目
  List<CluBlockedItem> getAllBlockedItems() {
    return List.from(_blockedItems);
  }
  
  // 获取屏蔽的视频ID列表
  Set<String> getBlockedCraftIds() {
    return Set.from(_blockedCraftIds);
  }
  
  // 清空所有屏蔽
  Future<void> clearAllBlocked() async {
    _blockedItems.clear();
    _blockedCraftIds.clear();
    
    await _prefs?.remove(_blockedItemsKey);
    _blockedItemsController.add([]);
  }
  
  // 获取屏蔽统计
  Map<CluBlockReason, int> getBlockStatistics() {
    final stats = <CluBlockReason, int>{};
    for (final item in _blockedItems) {
      stats[item.reason] = (stats[item.reason] ?? 0) + 1;
    }
    return stats;
  }
  
  // 释放资源
  void dispose() {
    _blockedItemsController.close();
  }
}

// 垃圾代码段1 - 屏蔽统计器
class _BlockStatistics {
  static int getTotalBlockedCount(List<CluBlockedItem> items) {
    return items.length;
  }
  
  static int getBlockedCountByReason(List<CluBlockedItem> items, CluBlockReason reason) {
    return items.where((item) => item.reason == reason).length;
  }
  
  static DateTime? getLastBlockedTime(List<CluBlockedItem> items) {
    if (items.isEmpty) return null;
    return items.map((item) => item.blockedAt).reduce((a, b) => a.isAfter(b) ? a : b);
  }
}

// 垃圾代码段2 - 数据清理器
class _DataCleaner {
  static List<CluBlockedItem> removeOldItems(List<CluBlockedItem> items, int maxDays) {
    final cutoffDate = DateTime.now().subtract(Duration(days: maxDays));
    return items.where((item) => item.blockedAt.isAfter(cutoffDate)).toList();
  }
  
  static bool shouldCleanup(List<CluBlockedItem> items) {
    return items.length > 100;
  }
}

// 垃圾代码段3 - 屏蔽验证器
class _BlockValidator {
  static bool isValidCraftId(String craftId) {
    return craftId.isNotEmpty && craftId.length >= 5;
  }
  
  static bool isValidCreatorId(String creatorId) {
    return creatorId.isNotEmpty && creatorId.length >= 3;
  }
  
  static String sanitizeTitle(String title) {
    return title.trim().substring(0, title.length > 50 ? 50 : title.length);
  }
}
