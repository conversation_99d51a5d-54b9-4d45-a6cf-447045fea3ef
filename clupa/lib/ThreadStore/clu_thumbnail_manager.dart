// Clupa视频缩略图管理器
// 处理视频缩略图的生成、缓存和管理

import 'dart:io';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:crypto/crypto.dart';

class CluThumbnailManager {
  static final CluThumbnailManager _instance = CluThumbnailManager._internal();
  static CluThumbnailManager get instance => _instance;
  CluThumbnailManager._internal();

  SharedPreferences? _prefs;
  static const String _thumbnailCacheKey = 'clu_thumbnail_cache';
  static const String _cacheVersionKey = 'clu_cache_version';
  static const int _currentCacheVersion = 1;
  
  // 内存缓存
  final Map<String, String> _memoryCache = {};
  
  // 缓存目录
  Directory? _cacheDirectory;
  
  // 缩略图配置
  static const int _thumbnailHeight = 300;
  static const int _thumbnailQuality = 90;
  static const int _timeMs = 1000; // 取第1秒的帧

  // 初始化管理器
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _initializeCacheDirectory();
      await _loadCacheFromPrefs();
      await _checkCacheVersion();
    } catch (e) {
      debugPrint('Failed to initialize thumbnail manager: $e');
    }
  }

  // 初始化缓存目录
  Future<void> _initializeCacheDirectory() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _cacheDirectory = Directory('${appDir.path}/video_thumbnails');
      
      if (!await _cacheDirectory!.exists()) {
        await _cacheDirectory!.create(recursive: true);
      }
    } catch (e) {
      debugPrint('Failed to initialize cache directory: $e');
    }
  }

  // 从SharedPreferences加载缓存映射
  Future<void> _loadCacheFromPrefs() async {
    try {
      final cacheJson = _prefs?.getString(_thumbnailCacheKey);
      if (cacheJson != null) {
        final Map<String, dynamic> cacheMap = jsonDecode(cacheJson);
        _memoryCache.clear();
        cacheMap.forEach((key, value) {
          _memoryCache[key] = value as String;
        });
      }
    } catch (e) {
      debugPrint('Failed to load cache from prefs: $e');
    }
  }

  // 保存缓存映射到SharedPreferences
  Future<void> _saveCacheToPrefs() async {
    try {
      final cacheJson = jsonEncode(_memoryCache);
      await _prefs?.setString(_thumbnailCacheKey, cacheJson);
    } catch (e) {
      debugPrint('Failed to save cache to prefs: $e');
    }
  }

  // 检查缓存版本，如果版本不匹配则清理缓存
  Future<void> _checkCacheVersion() async {
    try {
      final cachedVersion = _prefs?.getInt(_cacheVersionKey) ?? 0;
      if (cachedVersion != _currentCacheVersion) {
        await clearAllCache();
        await _prefs?.setInt(_cacheVersionKey, _currentCacheVersion);
      }
    } catch (e) {
      debugPrint('Failed to check cache version: $e');
    }
  }

  // 生成缓存key
  String _generateCacheKey(String videoPath) {
    final bytes = utf8.encode(videoPath);
    final digest = sha256.convert(bytes);
    return 'thumb_${digest.toString().substring(0, 16)}';
  }

  // 获取缓存文件路径
  String _getCacheFilePath(String cacheKey) {
    return '${_cacheDirectory!.path}/$cacheKey.jpg';
  }

  // 获取视频缩略图
  Future<String?> getThumbnail(String videoPath) async {
    try {
      final cacheKey = _generateCacheKey(videoPath);
      
      // 检查内存缓存
      if (_memoryCache.containsKey(cacheKey)) {
        final cachedPath = _memoryCache[cacheKey]!;
        if (await File(cachedPath).exists()) {
          return cachedPath;
        } else {
          // 缓存文件不存在，从内存缓存中移除
          _memoryCache.remove(cacheKey);
        }
      }
      
      // 检查磁盘缓存
      final cacheFilePath = _getCacheFilePath(cacheKey);
      if (await File(cacheFilePath).exists()) {
        _memoryCache[cacheKey] = cacheFilePath;
        await _saveCacheToPrefs();
        return cacheFilePath;
      }
      
      // 生成新的缩略图
      return await _generateThumbnail(videoPath, cacheKey);
    } catch (e) {
      debugPrint('Failed to get thumbnail for $videoPath: $e');
      return null;
    }
  }

  // 生成缩略图
  Future<String?> _generateThumbnail(String videoPath, String cacheKey) async {
    try {
      String? thumbnailPath;
      
      if (videoPath.startsWith('assets/')) {
        // Asset视频需要先复制到临时目录
        thumbnailPath = await _generateAssetThumbnail(videoPath, cacheKey);
      } else {
        // 本地文件或网络视频
        thumbnailPath = await _generateFileThumbnail(videoPath, cacheKey);
      }
      
      if (thumbnailPath != null) {
        _memoryCache[cacheKey] = thumbnailPath;
        await _saveCacheToPrefs();
      }
      
      return thumbnailPath;
    } catch (e) {
      debugPrint('Failed to generate thumbnail: $e');
      return null;
    }
  }

  // 为Asset视频生成缩略图
  Future<String?> _generateAssetThumbnail(String assetPath, String cacheKey) async {
    try {
      // 将asset复制到临时目录
      final tempDir = await getTemporaryDirectory();
      final tempVideoPath = '${tempDir.path}/temp_video_${DateTime.now().millisecondsSinceEpoch}.mp4';
      
      final byteData = await rootBundle.load(assetPath);
      final tempVideoFile = File(tempVideoPath);
      await tempVideoFile.writeAsBytes(byteData.buffer.asUint8List());
      
      // 生成缩略图
      final thumbnailPath = await VideoThumbnail.thumbnailFile(
        video: tempVideoPath,
        thumbnailPath: _getCacheFilePath(cacheKey),
        imageFormat: ImageFormat.JPEG,
        maxHeight: _thumbnailHeight,
        quality: _thumbnailQuality,
        timeMs: _timeMs,
      );
      
      // 清理临时文件
      await tempVideoFile.delete();
      
      return thumbnailPath;
    } catch (e) {
      debugPrint('Failed to generate asset thumbnail: $e');
      return null;
    }
  }

  // 为本地文件生成缩略图
  Future<String?> _generateFileThumbnail(String videoPath, String cacheKey) async {
    try {
      return await VideoThumbnail.thumbnailFile(
        video: videoPath,
        thumbnailPath: _getCacheFilePath(cacheKey),
        imageFormat: ImageFormat.JPEG,
        maxHeight: _thumbnailHeight,
        quality: _thumbnailQuality,
        timeMs: _timeMs,
      );
    } catch (e) {
      debugPrint('Failed to generate file thumbnail: $e');
      return null;
    }
  }

  // 预加载所有预设视频的缩略图
  Future<void> preloadAssetThumbnails(List<String> assetPaths) async {
    try {
      for (final assetPath in assetPaths) {
        await getThumbnail(assetPath);
      }
    } catch (e) {
      debugPrint('Failed to preload asset thumbnails: $e');
    }
  }

  // 检查缩略图是否存在
  bool hasThumbnail(String videoPath) {
    final cacheKey = _generateCacheKey(videoPath);
    return _memoryCache.containsKey(cacheKey);
  }

  // 获取缓存大小
  Future<int> getCacheSize() async {
    try {
      if (_cacheDirectory == null || !await _cacheDirectory!.exists()) {
        return 0;
      }
      
      int totalSize = 0;
      await for (final entity in _cacheDirectory!.list()) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }
      return totalSize;
    } catch (e) {
      debugPrint('Failed to get cache size: $e');
      return 0;
    }
  }

  // 清理所有缓存
  Future<void> clearAllCache() async {
    try {
      _memoryCache.clear();
      
      if (_cacheDirectory != null && await _cacheDirectory!.exists()) {
        await _cacheDirectory!.delete(recursive: true);
        await _cacheDirectory!.create(recursive: true);
      }
      
      await _prefs?.remove(_thumbnailCacheKey);
    } catch (e) {
      debugPrint('Failed to clear cache: $e');
    }
  }

  // 清理过期缓存（可选实现）
  Future<void> cleanExpiredCache({int maxAgeInDays = 30}) async {
    try {
      if (_cacheDirectory == null || !await _cacheDirectory!.exists()) {
        return;
      }
      
      final now = DateTime.now();
      final expiredKeys = <String>[];
      
      await for (final entity in _cacheDirectory!.list()) {
        if (entity is File) {
          final stat = await entity.stat();
          final age = now.difference(stat.modified).inDays;
          
          if (age > maxAgeInDays) {
            await entity.delete();
            // 从内存缓存中找到对应的key并移除
            final fileName = entity.path.split('/').last.split('.').first;
            expiredKeys.add(fileName);
          }
        }
      }
      
      // 从内存缓存中移除过期的条目
      for (final key in expiredKeys) {
        _memoryCache.removeWhere((k, v) => v.contains(key));
      }
      
      await _saveCacheToPrefs();
    } catch (e) {
      debugPrint('Failed to clean expired cache: $e');
    }
  }
}
