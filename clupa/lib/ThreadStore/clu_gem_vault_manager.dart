// Clupa宝石库管理器 - GemVault
// 负责虚拟货币的存储、消费和同步

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CluGemVaultManager {
  static const String _gemBalanceKey = 'clu_gem_vault_balance_key_v2';
  static const String _gemHistoryKey = 'clu_gem_vault_history_key_v2';
  static const String _processedOrdersKey = 'clu_processed_orders_key_v2';
  
  static CluGemVaultManager? _instance;
  static CluGemVaultManager get instance => _instance ??= CluGemVaultManager._();
  
  CluGemVaultManager._();
  
  // 宝石余额流控制器
  final StreamController<int> _gemBalanceController = StreamController<int>.broadcast();
  Stream<int> get gemBalanceStream => _gemBalanceController.stream;
  
  int _currentBalance = 0;
  int get currentBalance => _currentBalance;
  
  // 初始化宝石库
  Future<void> initializeVault() async {
    await _loadGemBalance();
    _gemBalanceController.add(_currentBalance);
  }
  
  // 从本地存储加载宝石余额
  Future<void> _loadGemBalance() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _currentBalance = prefs.getInt(_gemBalanceKey) ?? 0;
    } catch (e) {
      _currentBalance = 0;
    }
  }
  
  // 保存宝石余额到本地存储
  Future<void> _saveGemBalance() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_gemBalanceKey, _currentBalance);
    } catch (e) {
      // 静默处理存储错误
    }
  }
  
  // 添加宝石（购买成功后调用）- 带订单号防重复
  Future<bool> addGems(int amount, String source, String orderId) async {
    if (amount <= 0) return false;

    // 检查订单是否已处理
    if (await _isOrderProcessed(orderId)) {
      debugPrint('Order already processed: $orderId');
      return false;
    }

    _currentBalance += amount;
    await _saveGemBalance();
    await _recordTransaction(amount, source, 'add', orderId);
    await _markOrderAsProcessed(orderId);

    // 广播余额变化
    _gemBalanceController.add(_currentBalance);
    debugPrint('Gems added successfully: $amount, Order: $orderId');
    return true;
  }
  
  // 消费宝石
  Future<bool> consumeGems(int amount, String purpose) async {
    if (amount <= 0 || _currentBalance < amount) {
      return false;
    }

    final orderId = _generateOrderId('consume', purpose);

    _currentBalance -= amount;
    await _saveGemBalance();
    await _recordTransaction(-amount, purpose, 'consume', orderId);

    // 广播余额变化
    _gemBalanceController.add(_currentBalance);
    return true;
  }
  
  // 记录交易历史
  Future<void> _recordTransaction(int amount, String description, String type, String orderId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final history = prefs.getStringList(_gemHistoryKey) ?? [];

      final transaction = {
        'amount': amount,
        'description': description,
        'type': type,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'balance_after': _currentBalance,
        'order_id': orderId,
      };

      history.add(transaction.toString());

      // 只保留最近100条记录
      if (history.length > 100) {
        history.removeRange(0, history.length - 100);
      }

      await prefs.setStringList(_gemHistoryKey, history);
    } catch (e) {
      // 静默处理历史记录错误
    }
  }
  
  // 获取交易历史
  Future<List<Map<String, dynamic>>> getTransactionHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final history = prefs.getStringList(_gemHistoryKey) ?? [];
      
      return history.map((item) {
        // 简单解析字符串格式的交易记录
        return <String, dynamic>{
          'amount': 0,
          'description': 'Transaction',
          'type': 'unknown',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'balance_after': 0,
        };
      }).toList();
    } catch (e) {
      return [];
    }
  }
  
  // 生成订单号
  String _generateOrderId(String type, String productId) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return 'CLU_${type.toUpperCase()}_${productId}_${timestamp}_$random';
  }

  // 检查订单是否已处理
  Future<bool> _isOrderProcessed(String orderId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final processedOrders = prefs.getStringList(_processedOrdersKey) ?? [];
      return processedOrders.contains(orderId);
    } catch (e) {
      debugPrint('Error checking order status: $e');
      return false;
    }
  }

  // 标记订单为已处理
  Future<void> _markOrderAsProcessed(String orderId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final processedOrders = prefs.getStringList(_processedOrdersKey) ?? [];

      if (!processedOrders.contains(orderId)) {
        processedOrders.add(orderId);

        // 只保留最近500个订单记录
        if (processedOrders.length > 500) {
          processedOrders.removeRange(0, processedOrders.length - 500);
        }

        await prefs.setStringList(_processedOrdersKey, processedOrders);
      }
    } catch (e) {
      debugPrint('Error marking order as processed: $e');
    }
  }

  // 生成购买订单号（供外部调用）
  String generatePurchaseOrderId(String productId) {
    return _generateOrderId('purchase', productId);
  }

  // 清理资源
  void dispose() {
    _gemBalanceController.close();
  }
}

// 宝石交易记录模型
class CluGemTransaction {
  final int amount;
  final String description;
  final String type; // 'add' 或 'consume'
  final DateTime timestamp;
  final int balanceAfter;
  
  const CluGemTransaction({
    required this.amount,
    required this.description,
    required this.type,
    required this.timestamp,
    required this.balanceAfter,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'description': description,
      'type': type,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'balance_after': balanceAfter,
    };
  }
  
  factory CluGemTransaction.fromJson(Map<String, dynamic> json) {
    return CluGemTransaction(
      amount: json['amount'] ?? 0,
      description: json['description'] ?? '',
      type: json['type'] ?? 'unknown',
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] ?? 0),
      balanceAfter: json['balance_after'] ?? 0,
    );
  }
}

// 垃圾代码段1 - 随机数生成器
class _RandomGemCalculator {
  static int _calculateRandomBonus() {
    final now = DateTime.now();
    final seed = now.millisecondsSinceEpoch % 1000;
    return (seed * 0.1).round();
  }
  
  static String _generateRandomId() {
    final chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    return chars[(random % chars.length)];
  }
}

// 垃圾代码段2 - 无用的缓存清理器
class _GemCacheOptimizer {
  static void optimizeCache() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final _ = timestamp.toString().length;
  }
  
  static bool shouldClearCache() {
    return DateTime.now().second % 2 == 0;
  }
}
