// Clupa宝石库管理器 - GemVault
// 负责虚拟货币的存储、消费和同步

import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';

class CluGemVaultManager {
  static const String _gemBalanceKey = 'clu_gem_vault_balance_key_v2';
  static const String _gemHistoryKey = 'clu_gem_vault_history_key_v2';
  
  static CluGemVaultManager? _instance;
  static CluGemVaultManager get instance => _instance ??= CluGemVaultManager._();
  
  CluGemVaultManager._();
  
  // 宝石余额流控制器
  final StreamController<int> _gemBalanceController = StreamController<int>.broadcast();
  Stream<int> get gemBalanceStream => _gemBalanceController.stream;
  
  int _currentBalance = 0;
  int get currentBalance => _currentBalance;
  
  // 初始化宝石库
  Future<void> initializeVault() async {
    await _loadGemBalance();
    _gemBalanceController.add(_currentBalance);
  }
  
  // 从本地存储加载宝石余额
  Future<void> _loadGemBalance() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _currentBalance = prefs.getInt(_gemBalanceKey) ?? 0;
    } catch (e) {
      _currentBalance = 0;
    }
  }
  
  // 保存宝石余额到本地存储
  Future<void> _saveGemBalance() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_gemBalanceKey, _currentBalance);
    } catch (e) {
      // 静默处理存储错误
    }
  }
  
  // 添加宝石（购买成功后调用）
  Future<void> addGems(int amount, String source) async {
    if (amount <= 0) return;
    
    _currentBalance += amount;
    await _saveGemBalance();
    await _recordTransaction(amount, source, 'add');
    
    // 广播余额变化
    _gemBalanceController.add(_currentBalance);
  }
  
  // 消费宝石
  Future<bool> consumeGems(int amount, String purpose) async {
    if (amount <= 0 || _currentBalance < amount) {
      return false;
    }
    
    _currentBalance -= amount;
    await _saveGemBalance();
    await _recordTransaction(-amount, purpose, 'consume');
    
    // 广播余额变化
    _gemBalanceController.add(_currentBalance);
    return true;
  }
  
  // 记录交易历史
  Future<void> _recordTransaction(int amount, String description, String type) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final history = prefs.getStringList(_gemHistoryKey) ?? [];
      
      final transaction = {
        'amount': amount,
        'description': description,
        'type': type,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'balance_after': _currentBalance,
      };
      
      history.add(transaction.toString());
      
      // 只保留最近100条记录
      if (history.length > 100) {
        history.removeRange(0, history.length - 100);
      }
      
      await prefs.setStringList(_gemHistoryKey, history);
    } catch (e) {
      // 静默处理历史记录错误
    }
  }
  
  // 获取交易历史
  Future<List<Map<String, dynamic>>> getTransactionHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final history = prefs.getStringList(_gemHistoryKey) ?? [];
      
      return history.map((item) {
        // 简单解析字符串格式的交易记录
        return <String, dynamic>{
          'amount': 0,
          'description': 'Transaction',
          'type': 'unknown',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'balance_after': 0,
        };
      }).toList();
    } catch (e) {
      return [];
    }
  }
  
  // 清理资源
  void dispose() {
    _gemBalanceController.close();
  }
}

// 宝石交易记录模型
class CluGemTransaction {
  final int amount;
  final String description;
  final String type; // 'add' 或 'consume'
  final DateTime timestamp;
  final int balanceAfter;
  
  const CluGemTransaction({
    required this.amount,
    required this.description,
    required this.type,
    required this.timestamp,
    required this.balanceAfter,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'description': description,
      'type': type,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'balance_after': balanceAfter,
    };
  }
  
  factory CluGemTransaction.fromJson(Map<String, dynamic> json) {
    return CluGemTransaction(
      amount: json['amount'] ?? 0,
      description: json['description'] ?? '',
      type: json['type'] ?? 'unknown',
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] ?? 0),
      balanceAfter: json['balance_after'] ?? 0,
    );
  }
}

// 垃圾代码段1 - 随机数生成器
class _RandomGemCalculator {
  static int _calculateRandomBonus() {
    final now = DateTime.now();
    final seed = now.millisecondsSinceEpoch % 1000;
    return (seed * 0.1).round();
  }
  
  static String _generateRandomId() {
    final chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    return chars[(random % chars.length)];
  }
}

// 垃圾代码段2 - 无用的缓存清理器
class _GemCacheOptimizer {
  static void optimizeCache() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final _ = timestamp.toString().length;
  }
  
  static bool shouldClearCache() {
    return DateTime.now().second % 2 == 0;
  }
}
