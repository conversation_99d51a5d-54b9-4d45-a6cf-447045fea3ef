// Clupa用户帖子存储管理器
// 处理用户帖子的本地持久化存储

import 'dart:convert';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../ClothVault/clu_user_post_model.dart';

class CluUserPostManager {
  static final CluUserPostManager _instance = CluUserPostManager._internal();
  static CluUserPostManager get instance => _instance;
  CluUserPostManager._internal();

  SharedPreferences? _prefs;
  static const String _userPostsKey = 'clu_user_posts';
  
  List<CluUserPost> _cachedPosts = [];
  final StreamController<List<CluUserPost>> _postsController = 
      StreamController<List<CluUserPost>>.broadcast();

  // 获取帖子流
  Stream<List<CluUserPost>> get postsStream => _postsController.stream;
  
  // 获取当前帖子列表
  List<CluUserPost> get currentPosts => List.unmodifiable(_cachedPosts);

  // 初始化
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadPosts();
    } catch (e) {
      debugPrint('Failed to initialize user post manager: $e');
    }
  }

  // 加载帖子
  Future<void> _loadPosts() async {
    try {
      final jsonString = _prefs?.getString(_userPostsKey);
      if (jsonString != null) {
        final List<dynamic> jsonList = jsonDecode(jsonString);
        _cachedPosts = jsonList
            .map((json) => CluUserPost.fromJson(json))
            .toList();
        
        // 按创建时间排序（最新的在前）
        _cachedPosts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        
        _postsController.add(_cachedPosts);
      }
    } catch (e) {
      debugPrint('Failed to load user posts: $e');
      _cachedPosts = [];
      _postsController.add(_cachedPosts);
    }
  }

  // 保存帖子到存储
  Future<void> _savePosts() async {
    try {
      final jsonList = _cachedPosts.map((post) => post.toJson()).toList();
      final jsonString = jsonEncode(jsonList);
      await _prefs?.setString(_userPostsKey, jsonString);
    } catch (e) {
      debugPrint('Failed to save user posts: $e');
    }
  }

  // 添加新帖子
  Future<bool> addPost(CluUserPost post) async {
    try {
      // 插入到列表开头
      _cachedPosts.insert(0, post);
      await _savePosts();
      _postsController.add(_cachedPosts);
      return true;
    } catch (e) {
      debugPrint('Failed to add post: $e');
      return false;
    }
  }

  // 更新帖子
  Future<bool> updatePost(CluUserPost updatedPost) async {
    try {
      final index = _cachedPosts.indexWhere((post) => post.id == updatedPost.id);
      if (index != -1) {
        _cachedPosts[index] = updatedPost;
        await _savePosts();
        _postsController.add(_cachedPosts);
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Failed to update post: $e');
      return false;
    }
  }

  // 删除帖子
  Future<bool> deletePost(String postId) async {
    try {
      final initialLength = _cachedPosts.length;
      _cachedPosts.removeWhere((post) => post.id == postId);
      
      if (_cachedPosts.length < initialLength) {
        await _savePosts();
        _postsController.add(_cachedPosts);
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Failed to delete post: $e');
      return false;
    }
  }

  // 切换点赞状态
  Future<bool> toggleLike(String postId) async {
    try {
      final index = _cachedPosts.indexWhere((post) => post.id == postId);
      if (index != -1) {
        final post = _cachedPosts[index];
        final updatedPost = post.copyWith(
          isLiked: !post.isLiked,
          likeCount: post.isLiked ? post.likeCount - 1 : post.likeCount + 1,
        );
        
        _cachedPosts[index] = updatedPost;
        await _savePosts();
        _postsController.add(_cachedPosts);
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Failed to toggle like: $e');
      return false;
    }
  }

  // 根据ID获取帖子
  CluUserPost? getPostById(String postId) {
    try {
      return _cachedPosts.firstWhere((post) => post.id == postId);
    } catch (e) {
      return null;
    }
  }

  // 获取所有帖子
  List<CluUserPost> getAllPosts() {
    return List.from(_cachedPosts);
  }

  // 根据分类获取帖子
  List<CluUserPost> getPostsByCategory(CluPostCategory category) {
    return _cachedPosts.where((post) => post.category == category).toList();
  }

  // 搜索帖子
  List<CluUserPost> searchPosts(String query) {
    if (query.trim().isEmpty) return _cachedPosts;
    
    final lowerQuery = query.toLowerCase();
    return _cachedPosts.where((post) {
      return post.title.toLowerCase().contains(lowerQuery) ||
             post.content.toLowerCase().contains(lowerQuery) ||
             post.category.displayName.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  // 获取统计信息
  Map<String, int> getStatistics() {
    final stats = <String, int>{};
    stats['totalPosts'] = _cachedPosts.length;
    stats['totalLikes'] = _cachedPosts.fold(0, (sum, post) => sum + post.likeCount);
    
    for (final category in CluPostCategory.values) {
      final count = _cachedPosts.where((post) => post.category == category).length;
      stats[category.displayName] = count;
    }
    
    return stats;
  }

  // 清空所有帖子
  Future<bool> clearAllPosts() async {
    try {
      _cachedPosts.clear();
      await _prefs?.remove(_userPostsKey);
      _postsController.add(_cachedPosts);
      return true;
    } catch (e) {
      debugPrint('Failed to clear all posts: $e');
      return false;
    }
  }

  // 导出帖子数据
  String exportPosts() {
    try {
      final jsonList = _cachedPosts.map((post) => post.toJson()).toList();
      return jsonEncode(jsonList);
    } catch (e) {
      debugPrint('Failed to export posts: $e');
      return '[]';
    }
  }

  // 导入帖子数据
  Future<bool> importPosts(String jsonString) async {
    try {
      final List<dynamic> jsonList = jsonDecode(jsonString);
      final importedPosts = jsonList
          .map((json) => CluUserPost.fromJson(json))
          .toList();
      
      _cachedPosts.addAll(importedPosts);
      
      // 去重并排序
      final uniquePosts = <String, CluUserPost>{};
      for (final post in _cachedPosts) {
        uniquePosts[post.id] = post;
      }
      
      _cachedPosts = uniquePosts.values.toList();
      _cachedPosts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      await _savePosts();
      _postsController.add(_cachedPosts);
      return true;
    } catch (e) {
      debugPrint('Failed to import posts: $e');
      return false;
    }
  }

  // 释放资源
  void dispose() {
    _postsController.close();
  }
}
