// Clupa发布配额管理器
// 管理用户免费发布视频的次数和金币消费

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CluPublishQuotaManager {
  static final CluPublishQuotaManager _instance = CluPublishQuotaManager._internal();
  static CluPublishQuotaManager get instance => _instance;
  CluPublishQuotaManager._internal();

  SharedPreferences? _prefs;
  static const String _freePublishCountKey = 'clu_free_publish_count';
  static const String _lastResetDateKey = 'clu_last_reset_date';
  
  // 配置常量
  static const int maxFreePublishes = 3;  // 最大免费发布次数
  static const int publishCostInGems = 50; // 发布一次视频需要的金币数
  
  // 当前免费发布次数
  int _freePublishCount = maxFreePublishes;
  
  // 配额变化流
  final StreamController<int> _quotaController = StreamController<int>.broadcast();
  Stream<int> get quotaStream => _quotaController.stream;

  // 初始化管理器
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadQuotaData();
      await _checkDailyReset();
    } catch (e) {
      debugPrint('Failed to initialize publish quota manager: $e');
    }
  }

  // 加载配额数据
  Future<void> _loadQuotaData() async {
    try {
      _freePublishCount = _prefs?.getInt(_freePublishCountKey) ?? maxFreePublishes;
    } catch (e) {
      debugPrint('Failed to load quota data: $e');
      _freePublishCount = maxFreePublishes;
    }
  }

  // 保存配额数据
  Future<void> _saveQuotaData() async {
    try {
      await _prefs?.setInt(_freePublishCountKey, _freePublishCount);
      await _prefs?.setString(_lastResetDateKey, DateTime.now().toIso8601String());
    } catch (e) {
      debugPrint('Failed to save quota data: $e');
    }
  }

  // 检查是否需要每日重置
  Future<void> _checkDailyReset() async {
    try {
      final lastResetString = _prefs?.getString(_lastResetDateKey);
      if (lastResetString != null) {
        final lastReset = DateTime.parse(lastResetString);
        final now = DateTime.now();
        
        // 如果是新的一天，重置免费次数
        if (now.day != lastReset.day || 
            now.month != lastReset.month || 
            now.year != lastReset.year) {
          await _resetDailyQuota();
        }
      } else {
        // 首次使用，设置初始值
        await _saveQuotaData();
      }
    } catch (e) {
      debugPrint('Failed to check daily reset: $e');
    }
  }

  // 重置每日配额
  Future<void> _resetDailyQuota() async {
    _freePublishCount = maxFreePublishes;
    await _saveQuotaData();
    _quotaController.add(_freePublishCount);
    debugPrint('Daily quota reset: $_freePublishCount free publishes available');
  }

  // 获取剩余免费发布次数
  int get remainingFreePublishes => _freePublishCount;

  // 检查是否可以免费发布
  bool get canPublishForFree => _freePublishCount > 0;

  // 获取发布状态信息
  PublishStatus getPublishStatus(int currentGems) {
    if (canPublishForFree) {
      return PublishStatus.free;
    } else if (currentGems >= publishCostInGems) {
      return PublishStatus.paidAvailable;
    } else {
      return PublishStatus.insufficientFunds;
    }
  }

  // 消费一次免费发布机会
  Future<bool> consumeFreePublish() async {
    if (_freePublishCount > 0) {
      _freePublishCount--;
      await _saveQuotaData();
      _quotaController.add(_freePublishCount);
      debugPrint('Free publish consumed. Remaining: $_freePublishCount');
      return true;
    }
    return false;
  }

  // 获取发布提示文本
  String getPublishHintText(int currentGems) {
    final status = getPublishStatus(currentGems);
    
    switch (status) {
      case PublishStatus.free:
        return 'Free publishes remaining: $_freePublishCount';
      case PublishStatus.paidAvailable:
        return 'No free publishes left. Cost: $publishCostInGems gems (You have: $currentGems gems)';
      case PublishStatus.insufficientFunds:
        return 'No free publishes left. Need $publishCostInGems gems (You have: $currentGems gems)';
    }
  }

  // 获取发布按钮文本
  String getPublishButtonText(int currentGems) {
    final status = getPublishStatus(currentGems);
    
    switch (status) {
      case PublishStatus.free:
        return 'Publish Post (Free)';
      case PublishStatus.paidAvailable:
        return 'Publish Post ($publishCostInGems gems)';
      case PublishStatus.insufficientFunds:
        return 'Insufficient Gems';
    }
  }

  // 检查是否可以发布（免费或付费）
  bool canPublish(int currentGems) {
    return canPublishForFree || currentGems >= publishCostInGems;
  }

  // 手动重置配额（用于测试）
  Future<void> resetQuotaForTesting() async {
    await _resetDailyQuota();
  }

  // 手动设置免费次数（用于测试）
  Future<void> setFreePublishCountForTesting(int count) async {
    _freePublishCount = count.clamp(0, maxFreePublishes);
    await _saveQuotaData();
    _quotaController.add(_freePublishCount);
  }

  // 释放资源
  void dispose() {
    _quotaController.close();
  }
}

// 发布状态枚举
enum PublishStatus {
  free,              // 可以免费发布
  paidAvailable,     // 需要付费但金币足够
  insufficientFunds, // 金币不足
}

// 发布结果
class PublishResult {
  final bool success;
  final String message;
  final bool gemsConsumed;
  final int gemsSpent;

  const PublishResult({
    required this.success,
    required this.message,
    this.gemsConsumed = false,
    this.gemsSpent = 0,
  });

  factory PublishResult.success({bool gemsConsumed = false, int gemsSpent = 0}) {
    return PublishResult(
      success: true,
      message: gemsConsumed 
          ? 'Post published successfully! $gemsSpent gems consumed.'
          : 'Post published successfully!',
      gemsConsumed: gemsConsumed,
      gemsSpent: gemsSpent,
    );
  }

  factory PublishResult.failure(String message) {
    return PublishResult(
      success: false,
      message: message,
    );
  }

  factory PublishResult.insufficientFunds() {
    return PublishResult(
      success: false,
      message: 'Insufficient gems to publish. Please visit the gem store to purchase more gems.',
    );
  }
}
