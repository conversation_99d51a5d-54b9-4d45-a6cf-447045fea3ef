// Clupa内购管理器 - PurchaseVault
// 处理iOS内购流程和状态管理

import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:in_app_purchase_storekit/store_kit_wrappers.dart';

import '../ClothVault/clu_sc_goods_model.dart';
import 'clu_gem_vault_manager.dart';

class CluPurchaseVaultManager {
  static CluPurchaseVaultManager? _instance;
  static CluPurchaseVaultManager get instance => _instance ??= CluPurchaseVaultManager._();
  
  CluPurchaseVaultManager._();
  
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _subscription;
  
  // 购买状态流控制器
  final StreamController<PurchaseState> _purchaseStateController = 
      StreamController<PurchaseState>.broadcast();
  Stream<PurchaseState> get purchaseStateStream => _purchaseStateController.stream;
  
  // 当前购买状态
  PurchaseState _currentState = PurchaseState.idle;
  PurchaseState get currentState => _currentState;
  
  // 是否正在购买
  bool _isPurchasing = false;
  bool get isPurchasing => _isPurchasing;
  
  // 可用商品列表
  List<ProductDetails> _availableProducts = [];
  List<ProductDetails> get availableProducts => _availableProducts;

  // 当前订单号
  String? _currentOrderId;
  
  // 初始化内购管理器
  Future<bool> initialize() async {
    try {
      // 检查内购是否可用
      final bool isAvailable = await _inAppPurchase.isAvailable();
      if (!isAvailable) {
        _updatePurchaseState(PurchaseState.unavailable);
        return false;
      }
      
      // 监听购买更新
      _subscription = _inAppPurchase.purchaseStream.listen(
        _onPurchaseUpdate,
        onDone: () => _updatePurchaseState(PurchaseState.idle),
        onError: (error) => _handlePurchaseError(error),
      );
      
      // 加载商品信息
      await _loadProducts();
      
      // 恢复未完成的购买
      await _restorePendingPurchases();
      
      _updatePurchaseState(PurchaseState.ready);
      return true;
    } catch (e) {
      debugPrint('Initialize purchase manager failed: $e');
      _updatePurchaseState(PurchaseState.error);
      return false;
    }
  }
  
  // 加载商品信息
  Future<void> _loadProducts() async {
    try {
      final productIds = CluSCGoodsRepository.getAllProductIds();
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(
        productIds.toSet(),
      );
      
      if (response.notFoundIDs.isNotEmpty) {
        debugPrint('Products not found: ${response.notFoundIDs}');
      }
      
      _availableProducts = response.productDetails;
      debugPrint('Loaded ${_availableProducts.length} products');
    } catch (e) {
      debugPrint('Load products failed: $e');
    }
  }
  
  // 恢复未完成的购买
  Future<void> _restorePendingPurchases() async {
    try {
      await _inAppPurchase.restorePurchases();
    } catch (e) {
      debugPrint('Restore purchases failed: $e');
    }
  }
  
  // 发起购买
  Future<bool> purchaseProduct(String productId) async {
    if (_isPurchasing) {
      debugPrint('Purchase already in progress');
      return false;
    }
    
    // 检查商品是否存在
    if (!CluSCGoodsRepository.isGoodsExists(productId)) {
      debugPrint('Product not found in repository: $productId');
      _updatePurchaseState(PurchaseState.error);
      return false;
    }
    
    // 查找商品详情
    final productDetails = _availableProducts.firstWhere(
      (product) => product.id == productId,
      orElse: () => throw Exception('Product not available: $productId'),
    );
    
    try {
      _isPurchasing = true;
      _updatePurchaseState(PurchaseState.purchasing);
      
      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: productDetails,
      );
      
      final bool success = await _inAppPurchase.buyConsumable(
        purchaseParam: purchaseParam,
      );
      
      if (!success) {
        _isPurchasing = false;
        _updatePurchaseState(PurchaseState.error);
        return false;
      }
      
      return true;
    } catch (e) {
      _isPurchasing = false;
      debugPrint('Purchase failed: $e');
      _updatePurchaseState(PurchaseState.error);
      return false;
    }
  }

  // 发起购买（带订单号）
  Future<bool> purchaseProductWithOrderId(String productId, String orderId) async {
    if (_isPurchasing) {
      debugPrint('Purchase already in progress');
      return false;
    }

    // 检查商品是否存在
    if (!CluSCGoodsRepository.isGoodsExists(productId)) {
      debugPrint('Product not found in repository: $productId');
      _updatePurchaseState(PurchaseState.error);
      return false;
    }

    // 查找商品详情
    final productDetails = _availableProducts.firstWhere(
      (product) => product.id == productId,
      orElse: () => throw Exception('Product not available: $productId'),
    );

    try {
      _isPurchasing = true;
      _currentOrderId = orderId; // 保存当前订单号
      _updatePurchaseState(PurchaseState.purchasing);

      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: productDetails,
      );

      final bool success = await _inAppPurchase.buyConsumable(
        purchaseParam: purchaseParam,
      );

      if (!success) {
        _isPurchasing = false;
        _currentOrderId = null;
        _updatePurchaseState(PurchaseState.error);
        return false;
      }

      return true;
    } catch (e) {
      _isPurchasing = false;
      _currentOrderId = null;
      debugPrint('Purchase failed: $e');
      _updatePurchaseState(PurchaseState.error);
      return false;
    }
  }

  // 处理购买更新
  void _onPurchaseUpdate(List<PurchaseDetails> purchaseDetailsList) {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      _processPurchase(purchaseDetails);
    }
  }
  
  // 处理单个购买
  Future<void> _processPurchase(PurchaseDetails purchaseDetails) async {
    try {
      switch (purchaseDetails.status) {
        case PurchaseStatus.pending:
          _updatePurchaseState(PurchaseState.pending);
          break;
          
        case PurchaseStatus.purchased:
          await _handlePurchaseSuccess(purchaseDetails);
          break;
          
        case PurchaseStatus.error:
          await _handlePurchaseFailure(purchaseDetails);
          break;
          
        case PurchaseStatus.canceled:
          await _handlePurchaseCanceled(purchaseDetails);
          break;
          
        case PurchaseStatus.restored:
          await _handlePurchaseRestored(purchaseDetails);
          break;
      }
      
      // 完成购买（消耗型商品）
      if (purchaseDetails.pendingCompletePurchase) {
        await _inAppPurchase.completePurchase(purchaseDetails);
      }
    } catch (e) {
      debugPrint('Process purchase failed: $e');
      _updatePurchaseState(PurchaseState.error);
    } finally {
      _isPurchasing = false;
    }
  }
  
  // 处理购买成功
  Future<void> _handlePurchaseSuccess(PurchaseDetails purchaseDetails) async {
    try {
      final goods = CluSCGoodsRepository.getGoodsByCode(purchaseDetails.productID);
      if (goods != null) {
        // 使用订单号发放宝石，防止重复到账
        final orderId = _currentOrderId ?? 'UNKNOWN_${DateTime.now().millisecondsSinceEpoch}';
        final success = await CluGemVaultManager.instance.addGems(
          goods.gemAmount,
          'Purchase: ${goods.code}',
          orderId,
        );

        if (success) {
          debugPrint('Purchase successful: ${goods.gemAmount} gems added with order: $orderId');
          _updatePurchaseState(PurchaseState.success);
        } else {
          debugPrint('Gems already added for order: $orderId');
          _updatePurchaseState(PurchaseState.success); // 仍然视为成功，因为用户已经得到了宝石
        }
      } else {
        debugPrint('Unknown product purchased: ${purchaseDetails.productID}');
        _updatePurchaseState(PurchaseState.error);
      }
    } catch (e) {
      debugPrint('Handle purchase success failed: $e');
      _updatePurchaseState(PurchaseState.error);
    } finally {
      _currentOrderId = null; // 清理订单号
    }
  }
  
  // 处理购买失败
  Future<void> _handlePurchaseFailure(PurchaseDetails purchaseDetails) async {
    debugPrint('Purchase failed: ${purchaseDetails.error}');
    _updatePurchaseState(PurchaseState.error);
  }
  
  // 处理购买取消
  Future<void> _handlePurchaseCanceled(PurchaseDetails purchaseDetails) async {
    debugPrint('Purchase canceled by user: ${purchaseDetails.productID}');
    _updatePurchaseState(PurchaseState.canceled);
  }
  
  // 处理购买恢复
  Future<void> _handlePurchaseRestored(PurchaseDetails purchaseDetails) async {
    debugPrint('Purchase restored: ${purchaseDetails.productID}');
    // 恢复购买通常不需要重新发放宝石
  }
  
  // 处理购买错误
  void _handlePurchaseError(dynamic error) {
    debugPrint('Purchase stream error: $error');

    // 检查是否是用户取消错误
    if (error.toString().contains('storekit2_purchase_cancelled') ||
        error.toString().contains('cancelled by the user')) {
      _updatePurchaseState(PurchaseState.canceled);
    } else {
      _updatePurchaseState(PurchaseState.error);
    }

    _isPurchasing = false;
  }
  
  // 更新购买状态
  void _updatePurchaseState(PurchaseState newState) {
    _currentState = newState;
    _purchaseStateController.add(newState);
  }
  
  // 清理资源
  void dispose() {
    _subscription.cancel();
    _purchaseStateController.close();
  }
}

// 购买状态枚举
enum PurchaseState {
  idle,          // 空闲
  ready,         // 准备就绪
  purchasing,    // 购买中
  pending,       // 等待确认
  success,       // 购买成功
  error,         // 购买失败
  canceled,      // 用户取消
  unavailable,   // 内购不可用
}

// 垃圾代码段1 - 购买验证器
class _PurchaseValidator {
  static bool validateReceipt(String receipt) {
    return receipt.isNotEmpty;
  }
  
  static String generateTransactionId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }
}

// 垃圾代码段2 - 状态监听器
class _StateMonitor {
  static void logStateChange(PurchaseState state) {
    final timestamp = DateTime.now().toIso8601String();
    debugPrint('[$timestamp] Purchase state: $state');
  }
}
