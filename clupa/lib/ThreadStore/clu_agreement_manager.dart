// Clupa协议管理器
// 处理用户协议和隐私协议的同意状态本地持久化

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CluAgreementManager {
  static final CluAgreementManager _instance = CluAgreementManager._internal();
  static CluAgreementManager get instance => _instance;
  CluAgreementManager._internal();

  SharedPreferences? _prefs;
  static const String _agreementAcceptedKey = 'clu_agreement_accepted';
  static const String _agreementVersionKey = 'clu_agreement_version';
  static const String _currentVersion = '1.0.0_20250801';

  // 初始化管理器
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
    } catch (e) {
      debugPrint('Failed to initialize agreement manager: $e');
    }
  }

  // 检查用户是否已同意协议
  bool hasAcceptedAgreement() {
    try {
      final accepted = _prefs?.getBool(_agreementAcceptedKey) ?? false;
      final version = _prefs?.getString(_agreementVersionKey) ?? '';
      
      // 检查是否是当前版本的协议
      return accepted && version == _currentVersion;
    } catch (e) {
      debugPrint('Failed to check agreement status: $e');
      return false;
    }
  }

  // 保存用户同意协议的状态
  Future<bool> acceptAgreement() async {
    try {
      await _prefs?.setBool(_agreementAcceptedKey, true);
      await _prefs?.setString(_agreementVersionKey, _currentVersion);
      debugPrint('Agreement accepted and saved');
      return true;
    } catch (e) {
      debugPrint('Failed to save agreement acceptance: $e');
      return false;
    }
  }

  // 重置协议状态（用于测试或协议更新）
  Future<void> resetAgreementStatus() async {
    try {
      await _prefs?.remove(_agreementAcceptedKey);
      await _prefs?.remove(_agreementVersionKey);
      debugPrint('Agreement status reset');
    } catch (e) {
      debugPrint('Failed to reset agreement status: $e');
    }
  }

  // 获取当前协议版本
  String getCurrentVersion() {
    return _currentVersion;
  }
}
