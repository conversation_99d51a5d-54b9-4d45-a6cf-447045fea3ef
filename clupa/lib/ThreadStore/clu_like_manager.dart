// Clupa点赞管理器
// 处理视频点赞状态的本地持久化存储

import 'dart:convert';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CluLikeManager {
  static final CluLikeManager _instance = CluLikeManager._internal();
  static CluLikeManager get instance => _instance;
  CluLikeManager._internal();

  SharedPreferences? _prefs;
  static const String _likedCraftsKey = 'clu_liked_crafts';
  static const String _craftLikeCountsKey = 'clu_craft_like_counts';
  
  // 已点赞的作品ID集合
  Set<String> _likedCraftIds = {};
  
  // 作品点赞数缓存 (craftId -> likeCount)
  Map<String, int> _craftLikeCounts = {};
  
  // 点赞状态变化流
  final StreamController<Set<String>> _likeStatusController = 
      StreamController<Set<String>>.broadcast();
  
  Stream<Set<String>> get likeStatusStream => _likeStatusController.stream;

  // 初始化管理器
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadLikedCrafts();
      await _loadCraftLikeCounts();
    } catch (e) {
      debugPrint('Failed to initialize like manager: $e');
    }
  }

  // 加载已点赞的作品
  Future<void> _loadLikedCrafts() async {
    try {
      final likedCraftsJson = _prefs?.getString(_likedCraftsKey);
      if (likedCraftsJson != null) {
        final List<dynamic> likedList = jsonDecode(likedCraftsJson);
        _likedCraftIds = Set<String>.from(likedList);
      }
    } catch (e) {
      debugPrint('Failed to load liked crafts: $e');
      _likedCraftIds = {};
    }
  }

  // 加载作品点赞数
  Future<void> _loadCraftLikeCounts() async {
    try {
      final likeCountsJson = _prefs?.getString(_craftLikeCountsKey);
      if (likeCountsJson != null) {
        final Map<String, dynamic> countsMap = jsonDecode(likeCountsJson);
        _craftLikeCounts = countsMap.map((key, value) => MapEntry(key, value as int));
      }
    } catch (e) {
      debugPrint('Failed to load craft like counts: $e');
      _craftLikeCounts = {};
    }
  }

  // 保存已点赞的作品
  Future<void> _saveLikedCrafts() async {
    try {
      final likedList = _likedCraftIds.toList();
      final jsonString = jsonEncode(likedList);
      await _prefs?.setString(_likedCraftsKey, jsonString);
    } catch (e) {
      debugPrint('Failed to save liked crafts: $e');
    }
  }

  // 保存作品点赞数
  Future<void> _saveCraftLikeCounts() async {
    try {
      final jsonString = jsonEncode(_craftLikeCounts);
      await _prefs?.setString(_craftLikeCountsKey, jsonString);
    } catch (e) {
      debugPrint('Failed to save craft like counts: $e');
    }
  }

  // 检查作品是否已点赞
  bool isLiked(String craftId) {
    return _likedCraftIds.contains(craftId);
  }

  // 获取作品点赞数
  int getLikeCount(String craftId, int originalCount) {
    return _craftLikeCounts[craftId] ?? originalCount;
  }

  // 切换点赞状态
  Future<bool> toggleLike(String craftId, int originalLikeCount) async {
    try {
      final wasLiked = _likedCraftIds.contains(craftId);
      final currentCount = _craftLikeCounts[craftId] ?? originalLikeCount;
      
      if (wasLiked) {
        // 取消点赞
        _likedCraftIds.remove(craftId);
        _craftLikeCounts[craftId] = (currentCount - 1).clamp(0, double.infinity).toInt();
      } else {
        // 添加点赞
        _likedCraftIds.add(craftId);
        _craftLikeCounts[craftId] = currentCount + 1;
      }

      // 保存到本地存储
      await _saveLikedCrafts();
      await _saveCraftLikeCounts();
      
      // 通知状态变化
      _likeStatusController.add(Set.from(_likedCraftIds));
      
      return !wasLiked; // 返回新的点赞状态
    } catch (e) {
      debugPrint('Failed to toggle like: $e');
      return false;
    }
  }

  // 获取所有已点赞的作品ID
  Set<String> getLikedCraftIds() {
    return Set.from(_likedCraftIds);
  }

  // 清除所有点赞数据
  Future<void> clearAllLikes() async {
    try {
      _likedCraftIds.clear();
      _craftLikeCounts.clear();
      
      await _prefs?.remove(_likedCraftsKey);
      await _prefs?.remove(_craftLikeCountsKey);
      
      _likeStatusController.add(Set.from(_likedCraftIds));
    } catch (e) {
      debugPrint('Failed to clear likes: $e');
    }
  }

  // 批量设置点赞状态（用于初始化）
  Future<void> initializeCraftLikes(Map<String, int> initialCounts) async {
    try {
      // 只更新没有本地记录的作品
      for (final entry in initialCounts.entries) {
        if (!_craftLikeCounts.containsKey(entry.key)) {
          _craftLikeCounts[entry.key] = entry.value;
        }
      }
      
      await _saveCraftLikeCounts();
    } catch (e) {
      debugPrint('Failed to initialize craft likes: $e');
    }
  }

  // 释放资源
  void dispose() {
    _likeStatusController.close();
  }
}
