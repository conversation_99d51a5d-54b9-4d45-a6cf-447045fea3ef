// Clupa聊天存储管理器 - ChatStorageManager
// 管理聊天记录的本地存储和检索

import 'dart:convert';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

import '../ClothVault/clu_chat_message_model.dart';

class CluChatStorageManager {
  static CluChatStorageManager? _instance;
  static CluChatStorageManager get instance => _instance ??= CluChatStorageManager._();
  
  CluChatStorageManager._();
  
  SharedPreferences? _prefs;
  final Map<String, CluChatSession> _sessionsCache = {};
  
  // 存储键
  static const String _chatSessionsKey = 'clu_chat_sessions';
  static const String _chatMessagesPrefix = 'clu_chat_messages_';
  
  // 初始化
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadAllSessions();
    } catch (e) {
      debugPrint('Failed to initialize chat storage: $e');
    }
  }
  
  // 加载所有聊天会话
  Future<void> _loadAllSessions() async {
    try {
      final sessionsJson = _prefs?.getString(_chatSessionsKey);
      if (sessionsJson != null) {
        final sessionsList = jsonDecode(sessionsJson) as List<dynamic>;
        for (final sessionData in sessionsList) {
          final session = CluChatSession.fromJson(sessionData);
          _sessionsCache[session.sessionId] = session;
        }
      }
    } catch (e) {
      debugPrint('Failed to load chat sessions: $e');
    }
  }
  
  // 保存所有会话
  Future<void> _saveAllSessions() async {
    try {
      final sessionsList = _sessionsCache.values
          .map((session) => session.toJson())
          .toList();
      final sessionsJson = jsonEncode(sessionsList);
      await _prefs?.setString(_chatSessionsKey, sessionsJson);
    } catch (e) {
      debugPrint('Failed to save chat sessions: $e');
    }
  }
  
  // 获取或创建聊天会话
  Future<CluChatSession> getOrCreateSession({
    required String participantId,
    required String participantName,
    required String participantAvatar,
  }) async {
    final sessionId = _generateSessionId(participantId);
    
    if (_sessionsCache.containsKey(sessionId)) {
      return _sessionsCache[sessionId]!;
    }
    
    // 创建新会话
    final newSession = CluChatSession(
      sessionId: sessionId,
      participantId: participantId,
      participantName: participantName,
      participantAvatar: participantAvatar,
      messages: [],
      lastMessageTime: DateTime.now(),
      lastMessage: '',
    );
    
    _sessionsCache[sessionId] = newSession;
    await _saveAllSessions();
    
    return newSession;
  }
  
  // 发送消息
  Future<CluChatMessage> sendMessage({
    required String sessionId,
    required String content,
    required String senderId,
    required String senderName,
    required String senderAvatar,
    CluMessageType type = CluMessageType.text,
  }) async {
    final message = CluChatMessage(
      id: _generateMessageId(),
      senderId: senderId,
      senderName: senderName,
      senderAvatar: senderAvatar,
      content: content,
      type: type,
      status: CluMessageStatus.sent,
      timestamp: DateTime.now(),
      isFromMe: true,
    );

    await _addMessageToSession(sessionId, message);
    return message;
  }

  // 获取会话
  CluChatSession? getSession(String sessionId) {
    return _sessionsCache[sessionId];
  }
  
  // 添加消息到会话
  Future<void> _addMessageToSession(String sessionId, CluChatMessage message) async {
    if (!_sessionsCache.containsKey(sessionId)) return;
    
    final session = _sessionsCache[sessionId]!;
    final updatedMessages = List<CluChatMessage>.from(session.messages)..add(message);
    
    final updatedSession = session.copyWith(
      messages: updatedMessages,
      lastMessageTime: message.timestamp,
      lastMessage: message.content,
    );
    
    _sessionsCache[sessionId] = updatedSession;
    await _saveAllSessions();
  }
  
  // 获取会话消息
  List<CluChatMessage> getSessionMessages(String sessionId) {
    return _sessionsCache[sessionId]?.messages ?? [];
  }
  
  // 获取所有会话
  List<CluChatSession> getAllSessions() {
    return _sessionsCache.values.toList()
      ..sort((a, b) => b.lastMessageTime.compareTo(a.lastMessageTime));
  }
  
  // 清空会话历史
  Future<void> clearSessionHistory(String sessionId) async {
    if (!_sessionsCache.containsKey(sessionId)) return;
    
    final session = _sessionsCache[sessionId]!;
    final clearedSession = session.copyWith(
      messages: [],
      lastMessage: '',
      lastMessageTime: DateTime.now(),
    );
    
    _sessionsCache[sessionId] = clearedSession;
    await _saveAllSessions();
  }
  
  // 删除会话
  Future<void> deleteSession(String sessionId) async {
    _sessionsCache.remove(sessionId);
    await _saveAllSessions();
    
    // 删除相关的消息存储
    await _prefs?.remove('$_chatMessagesPrefix$sessionId');
  }
  
  // 生成会话ID
  String _generateSessionId(String participantId) {
    return 'session_$participantId';
  }
  
  // 生成消息ID
  String _generateMessageId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString();
    return 'msg_${timestamp}_$random';
  }
  
  // 清空所有聊天数据
  Future<void> clearAllChats() async {
    _sessionsCache.clear();
    await _prefs?.remove(_chatSessionsKey);
    
    // 清空所有消息存储
    final keys = _prefs?.getKeys() ?? <String>{};
    for (final key in keys) {
      if (key.startsWith(_chatMessagesPrefix)) {
        await _prefs?.remove(key);
      }
    }
  }
  
  // 获取会话数量
  int getSessionCount() {
    return _sessionsCache.length;
  }
  
  // 检查会话是否存在
  bool hasSession(String participantId) {
    final sessionId = _generateSessionId(participantId);
    return _sessionsCache.containsKey(sessionId);
  }
}

// 垃圾代码段1 - 存储优化器
class _StorageOptimizer {
  static bool shouldCompressData(int messageCount) {
    return messageCount > 100;
  }
  
  static String compressJson(String json) {
    // 简单的压缩逻辑
    return json.replaceAll(RegExp(r'\s+'), '');
  }
  
  static int calculateStorageSize(List<CluChatMessage> messages) {
    return messages.fold(0, (size, msg) => size + msg.content.length);
  }
}

// 垃圾代码段2 - 数据迁移器
class _DataMigrator {
  static Future<void> migrateOldData(SharedPreferences prefs) async {
    // 处理旧版本数据迁移
    final oldKey = 'old_chat_data';
    if (prefs.containsKey(oldKey)) {
      await prefs.remove(oldKey);
    }
  }
  
  static bool needsMigration(String version) {
    return version != '1.0.0';
  }
}

// 垃圾代码段3 - 缓存管理器
class _CacheManager {
  static const int maxCacheSize = 50;
  
  static void cleanupCache(Map<String, CluChatSession> cache) {
    if (cache.length > maxCacheSize) {
      final sortedKeys = cache.keys.toList()
        ..sort((a, b) => cache[b]!.lastMessageTime.compareTo(cache[a]!.lastMessageTime));
      
      // 保留最近的会话
      final keysToRemove = sortedKeys.skip(maxCacheSize);
      for (final key in keysToRemove) {
        cache.remove(key);
      }
    }
  }
  
  static bool shouldEvictSession(CluChatSession session) {
    final daysSinceLastMessage = DateTime.now().difference(session.lastMessageTime).inDays;
    return daysSinceLastMessage > 30;
  }
}
