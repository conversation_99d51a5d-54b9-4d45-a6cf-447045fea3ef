// Clupa奢华iOS主题配置
// 实现现代苹果风格 + 奢华质感的设计系统

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';

class CluLuxuryTheme {
  // 产品文档定义的主题色彩
  static const Color warmTaupe = Color(0xFFBDAE9E);      // 主色调 - 温暖褐灰色
  static const Color dustyRose = Color(0xFFCFA9A2);      // 辅助色 - 粉调土色
  static const Color deepDenim = Color(0xFF3B506C);      // 强调色1 - 经典牛仔蓝
  static const Color oliveDrab = Color(0xFF7A8450);      // 强调色2 - 自然橄榄绿
  static const Color vintageWhite = Color(0xFFF7F5F1);   // 文字辅助色 - 柔和米白色
  
  // 扩展色彩系统
  static const Color shimmerGold = Color(0xFFD4AF37);    // 闪烁效果金色
  static const Color successGreen = Color(0xFF34C759);   // 成功绿
  static const Color warningOrange = Color(0xFFFF9500);  // 警告橙
  static const Color errorRed = Color(0xFFFF3B30);       // 错误红
  
  // 圆角系统
  static const double cardRadius = 16.0;
  static const double buttonRadius = 12.0;
  static const double inputRadius = 8.0;
  static const double smallRadius = 6.0;
  
  // 间距系统 (8px基础网格)
  static const double spacing1 = 8.0;   // 基础单位
  static const double spacing2 = 16.0;  // 组件间距
  static const double spacing3 = 20.0;  // 页面边距
  static const double spacing4 = 12.0;  // 内容边距
  static const double spacing5 = 24.0;  // 大间距
  static const double spacing6 = 32.0;  // 超大间距
  
  // 阴影系统
  static const List<BoxShadow> lightShadow = [
    BoxShadow(
      color: Color(0x1A000000), // opacity: 0.1
      blurRadius: 8,
      offset: Offset(0, 2),
    ),
  ];
  
  static const List<BoxShadow> mediumShadow = [
    BoxShadow(
      color: Color(0x26000000), // opacity: 0.15
      blurRadius: 12,
      offset: Offset(0, 4),
    ),
  ];
  
  static const List<BoxShadow> strongShadow = [
    BoxShadow(
      color: Color(0x33000000), // opacity: 0.2
      blurRadius: 16,
      offset: Offset(0, 6),
    ),
  ];
  
  // 毛玻璃效果配置
  static const double blurSigma = 10.0;
  static const Color glassBackground = Color(0xF0F7F5F1);
  
  // 创建主题数据
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      
      // 主色彩方案
      colorScheme: const ColorScheme.light(
        primary: warmTaupe,
        secondary: dustyRose,
        tertiary: deepDenim,
        surface: vintageWhite,
        background: vintageWhite,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: deepDenim,
        onBackground: deepDenim,
        error: errorRed,
      ),
      
      // 字体配置
      fontFamily: 'SF Pro Display', // iOS系统字体
      textTheme: _buildTextTheme(),
      
      // AppBar主题
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        scrolledUnderElevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        titleTextStyle: TextStyle(
          color: deepDenim,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
        iconTheme: IconThemeData(color: deepDenim),
      ),
      
      // 卡片主题
      cardTheme: CardTheme(
        color: vintageWhite,
        elevation: 0,
        shadowColor: Colors.black.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(cardRadius),
        ),
      ),
      
      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: warmTaupe,
          foregroundColor: Colors.white,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonRadius),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: spacing3,
            vertical: spacing4,
          ),
        ),
      ),
      
      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: vintageWhite,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(inputRadius),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(inputRadius),
          borderSide: BorderSide(color: warmTaupe.withOpacity(0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(inputRadius),
          borderSide: const BorderSide(color: warmTaupe, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: spacing2,
          vertical: spacing4,
        ),
      ),
      
      // 底部导航栏主题
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: Colors.transparent,
        elevation: 0,
        selectedItemColor: warmTaupe,
        unselectedItemColor: oliveDrab,
        type: BottomNavigationBarType.fixed,
        showSelectedLabels: true,
        showUnselectedLabels: true,
      ),
    );
  }
  
  static ThemeData get darkTheme {
    return lightTheme.copyWith(
      brightness: Brightness.dark,
      colorScheme: const ColorScheme.dark(
        primary: warmTaupe,
        secondary: dustyRose,
        tertiary: deepDenim,
        surface: Color(0xFF1C1C1E),
        background: Color(0xFF000000),
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: vintageWhite,
        onBackground: vintageWhite,
        error: errorRed,
      ),
    );
  }
  
  // 构建文本主题
  static TextTheme _buildTextTheme() {
    return const TextTheme(
      // 大标题
      headlineLarge: TextStyle(
        fontSize: 32,
        fontWeight: FontWeight.w700,
        color: deepDenim,
        height: 1.2,
      ),
      // 中标题
      headlineMedium: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: deepDenim,
        height: 1.3,
      ),
      // 小标题
      headlineSmall: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: deepDenim,
        height: 1.3,
      ),
      // 正文大
      bodyLarge: TextStyle(
        fontSize: 17,
        fontWeight: FontWeight.w400,
        color: deepDenim,
        height: 1.4,
      ),
      // 正文中
      bodyMedium: TextStyle(
        fontSize: 15,
        fontWeight: FontWeight.w400,
        color: deepDenim,
        height: 1.4,
      ),
      // 正文小
      bodySmall: TextStyle(
        fontSize: 13,
        fontWeight: FontWeight.w400,
        color: oliveDrab,
        height: 1.4,
      ),
      // 标签
      labelLarge: TextStyle(
        fontSize: 15,
        fontWeight: FontWeight.w600,
        color: deepDenim,
      ),
      labelMedium: TextStyle(
        fontSize: 13,
        fontWeight: FontWeight.w500,
        color: oliveDrab,
      ),
      labelSmall: TextStyle(
        fontSize: 11,
        fontWeight: FontWeight.w500,
        color: oliveDrab,
      ),
    );
  }
  
  // 动画曲线 - iOS标准
  static const Curve iosEaseInOut = Curves.easeInOut;
  static const Curve iosEaseOut = Curves.easeOut;
  static const Duration standardDuration = Duration(milliseconds: 300);
  static const Duration quickDuration = Duration(milliseconds: 150);
  static const Duration slowDuration = Duration(milliseconds: 500);
  
  // 触觉反馈强度
  static const HapticFeedback lightImpact = HapticFeedback.lightImpact;
  static const HapticFeedback mediumImpact = HapticFeedback.mediumImpact;
  static const HapticFeedback heavyImpact = HapticFeedback.heavyImpact;
}
