// Clupa AI聊天服务
// 处理Moonshot AI API调用和本地聊天记录存储

import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

import '../ClothVault/clu_sage_chat_model.dart';

class CluSageChatService {
  static const String _baseUrl = 'https://api.moonshot.cn/v1/chat/completions';
  static const String _apiKey = 'sk-UjLDXgyVsdh4kNtrLbbu1yFFhuafggJTxEw03ezpnnvXX2fR';
  static const String _model = 'moonshot-v1-8k';
  static const String _storagePrefix = 'clu_chat_history_';
  
  static final CluSageChatService _instance = CluSageChatService._internal();
  factory CluSageChatService() => _instance;
  CluSageChatService._internal();

  final Uuid _uuid = const Uuid();

  // 获取指定模式的聊天历史
  Future<CluChatHistory> getChatHistory(CluSageMode mode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = '$_storagePrefix${mode.index}';
      final jsonString = prefs.getString(key);
      
      if (jsonString != null) {
        final json = jsonDecode(jsonString);
        return CluChatHistory.fromJson(json);
      }
    } catch (e) {
      debugPrint('获取聊天历史失败: $e');
    }
    
    // 返回空的聊天历史，包含欢迎消息
    return CluChatHistory(
      mode: mode,
      messages: [
        CluChatMessage(
          id: _uuid.v4(),
          content: mode.welcomeMessage,
          isUser: false,
          timestamp: DateTime.now(),
          mode: mode,
        ),
      ],
      lastUpdated: DateTime.now(),
    );
  }

  // 保存聊天历史
  Future<void> saveChatHistory(CluChatHistory history) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = '$_storagePrefix${history.mode.index}';
      final jsonString = jsonEncode(history.toJson());
      await prefs.setString(key, jsonString);
    } catch (e) {
      debugPrint('保存聊天历史失败: $e');
    }
  }

  // 清空指定模式的聊天历史
  Future<void> clearChatHistory(CluSageMode mode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = '$_storagePrefix${mode.index}';
      await prefs.remove(key);
    } catch (e) {
      debugPrint('清空聊天历史失败: $e');
    }
  }

  // 发送消息到AI并获取回复
  Future<String> sendMessage(String userMessage, CluSageMode mode, List<CluChatMessage> chatHistory) async {
    try {
      // 构建消息历史，限制最近10条消息以控制token使用
      final recentMessages = chatHistory.where((msg) => !msg.content.contains(mode.welcomeMessage)).take(10).toList();
      
      final messages = [
        {
          'role': 'system',
          'content': mode.systemPrompt,
        },
        // 添加最近的对话历史
        ...recentMessages.map((msg) => {
          'role': msg.isUser ? 'user' : 'assistant',
          'content': msg.content,
        }),
        // 添加当前用户消息
        {
          'role': 'user',
          'content': userMessage,
        },
      ];

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode({
          'model': _model,
          'messages': messages,
          'temperature': 0.3,
          'max_tokens': 1000,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final aiResponse = data['choices'][0]['message']['content'];
        return aiResponse;
      } else {
        debugPrint('API请求失败: ${response.statusCode} - ${response.body}');
        return _getErrorMessage(response.statusCode);
      }
    } catch (e) {
      debugPrint('发送消息失败: $e');
      if (e is SocketException) {
        return "Network connection error. Please check your internet connection and try again.";
      }
      return "Sorry, I'm having trouble responding right now. Please try again in a moment.";
    }
  }

  String _getErrorMessage(int statusCode) {
    switch (statusCode) {
      case 401:
        return "Authentication error. Please contact support.";
      case 429:
        return "Too many requests. Please wait a moment and try again.";
      case 500:
        return "Server error. Please try again later.";
      default:
        return "Something went wrong. Please try again.";
    }
  }

  // 添加消息到聊天历史
  Future<CluChatHistory> addMessage(CluChatHistory history, String content, bool isUser) async {
    final newMessage = CluChatMessage(
      id: _uuid.v4(),
      content: content,
      isUser: isUser,
      timestamp: DateTime.now(),
      mode: history.mode,
    );

    final updatedHistory = history.copyWith(
      messages: [...history.messages, newMessage],
      lastUpdated: DateTime.now(),
    );

    await saveChatHistory(updatedHistory);
    return updatedHistory;
  }

  // 获取所有模式的聊天历史（用于调试或统计）
  Future<Map<CluSageMode, CluChatHistory>> getAllChatHistories() async {
    final histories = <CluSageMode, CluChatHistory>{};
    
    for (final mode in CluSageMode.values) {
      histories[mode] = await getChatHistory(mode);
    }
    
    return histories;
  }
}
