// Clupa AI助手页面 - AiSage
// 智能拼布指导和对话功能

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../WeaveRoutes/clu_luxury_theme.dart';
import '../FiberParts/clu_luxury_button.dart';
import '../FiberParts/clu_luxury_card.dart';

class CluAiSageView extends StatefulWidget {
  const CluAiSageView({super.key});

  @override
  State<CluAiSageView> createState() => _CluAiSageViewState();
}

class _CluAiSageViewState extends State<CluAiSageView>
    with AutomaticKeepAliveClientMixin {

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Container(
      color: CluLuxuryTheme.vintageWhite,
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(
            CluLuxuryTheme.spacing3,
            CluLuxuryTheme.spacing3,
            CluLuxuryTheme.spacing3,
            80, // 为底部导航栏留出空间
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    CupertinoIcons.sparkles,
                    size: 32,
                    color: CluLuxuryTheme.warmTaupe,
                  ),
                  const SizedBox(width: CluLuxuryTheme.spacing2),
                  Text(
                    'Pattern Sage',
                    style: Theme.of(context).textTheme.headlineMedium,
                  ),
                ],
              ),
              
              const SizedBox(height: CluLuxuryTheme.spacing1),
              
              Text(
                'Your AI quilting companion',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: CluLuxuryTheme.oliveDrab,
                ),
              ),
              
              const SizedBox(height: CluLuxuryTheme.spacing5),
              
              Expanded(
                child: ListView(
                  children: [
                    _buildSageFeature(
                      icon: CupertinoIcons.chat_bubble_2_fill,
                      title: 'Ask Questions',
                      subtitle: 'Get expert advice on techniques, materials, and patterns',
                      onTap: () => _showComingSoon('AI Chat'),
                    ),
                    
                    const SizedBox(height: CluLuxuryTheme.spacing2),
                    
                    _buildSageFeature(
                      icon: CupertinoIcons.color_filter,
                      title: 'Color Harmony',
                      subtitle: 'Find perfect color combinations for your project',
                      onTap: () => _showComingSoon('Color Advisor'),
                    ),
                    
                    const SizedBox(height: CluLuxuryTheme.spacing2),
                    
                    _buildSageFeature(
                      icon: CupertinoIcons.square_grid_3x2_fill,
                      title: 'Pattern Generator',
                      subtitle: 'Create unique quilt patterns with AI assistance',
                      onTap: () => _showComingSoon('Pattern Generator'),
                    ),
                    
                    const SizedBox(height: CluLuxuryTheme.spacing2),
                    
                    _buildSageFeature(
                      icon: CupertinoIcons.camera_viewfinder,
                      title: 'Fabric Scanner',
                      subtitle: 'Identify fabric types and get usage suggestions',
                      onTap: () => _showComingSoon('Fabric Scanner'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSageFeature({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return CluLuxuryCard(
      onTap: onTap,
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: CluLuxuryTheme.warmTaupe.withOpacity(0.1),
              borderRadius: BorderRadius.circular(CluLuxuryTheme.smallRadius),
            ),
            child: Icon(
              icon,
              size: 28,
              color: CluLuxuryTheme.warmTaupe,
            ),
          ),
          
          const SizedBox(width: CluLuxuryTheme.spacing2),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: CluLuxuryTheme.deepDenim,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 14,
                    color: CluLuxuryTheme.oliveDrab,
                  ),
                ),
              ],
            ),
          ),
          
          const Icon(
            CupertinoIcons.chevron_right,
            size: 20,
            color: CluLuxuryTheme.oliveDrab,
          ),
        ],
      ),
    );
  }

  void _showComingSoon(String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$feature Coming Soon!'),
        content: const Text('Our AI features are being fine-tuned and will be available soon.'),
        actions: [
          CluLuxuryButton(
            text: 'OK',
            onPressed: () => Navigator.of(context).pop(),
            size: CluButtonSize.small,
          ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
