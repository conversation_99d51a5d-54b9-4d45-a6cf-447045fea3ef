// Clupa宝石集市页面 - GemBazaar
// 个性化商城布局，支持内购功能

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'dart:async';

import '../WeaveRoutes/clu_luxury_theme.dart';
import '../FiberParts/clu_luxury_button.dart';
import '../FiberParts/clu_luxury_card.dart';
import '../ClothVault/clu_sc_goods_model.dart';
import '../ThreadStore/clu_gem_vault_manager.dart';
import '../ThreadStore/clu_purchase_vault_manager.dart';

class CluGemBazaarView extends StatefulWidget {
  const CluGemBazaarView({super.key});

  @override
  State<CluGemBazaarView> createState() => _CluGemBazaarViewState();
}

class _CluGemBazaarViewState extends State<CluGemBazaarView>
    with TickerProviderStateMixin {
  
  late AnimationController _shimmerController;
  late AnimationController _pulseController;
  late Animation<double> _shimmerAnimation;
  late Animation<double> _pulseAnimation;
  
  StreamSubscription? _gemBalanceSubscription;
  StreamSubscription? _purchaseStateSubscription;
  
  int _currentGemBalance = 0;
  bool _isLoading = false;
  bool _isPurchasing = false;
  String? _purchasingProductId;
  
  final List<CluSCGoods> _goodsList = CluSCGoodsRepository.getAllGoods();
  
  @override
  void initState() {
    super.initState();
    debugPrint('CluGemBazaarView initState called');
    _initializeAnimations();
    _initializeManagers();
    _setupSubscriptions();
  }
  
  void _initializeAnimations() {
    _shimmerController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);
    
    _shimmerAnimation = Tween<double>(
      begin: -1.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _shimmerController,
      curve: Curves.easeInOut,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 0.95,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }
  
  Future<void> _initializeManagers() async {
    setState(() => _isLoading = true);
    
    try {
      await CluGemVaultManager.instance.initializeVault();
      await CluPurchaseVaultManager.instance.initialize();
    } catch (e) {
      debugPrint('Initialize managers failed: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }
  
  void _setupSubscriptions() {
    // 监听宝石余额变化
    _gemBalanceSubscription = CluGemVaultManager.instance.gemBalanceStream.listen(
      (balance) {
        if (mounted) {
          setState(() => _currentGemBalance = balance);
        }
      },
    );
    
    // 监听购买状态变化
    _purchaseStateSubscription = CluPurchaseVaultManager.instance.purchaseStateStream.listen(
      _handlePurchaseStateChange,
    );
    
    // 初始化余额
    _currentGemBalance = CluGemVaultManager.instance.currentBalance;
  }
  
  void _handlePurchaseStateChange(PurchaseState state) {
    if (!mounted) return;
    
    switch (state) {
      case PurchaseState.purchasing:
        setState(() => _isPurchasing = true);
        _showLoadingDialog();
        break;
        
      case PurchaseState.pending:
        // 保持加载状态
        break;
        
      case PurchaseState.success:
        _hideLoadingDialog();
        setState(() {
          _isPurchasing = false;
          _purchasingProductId = null;
        });
        _showSuccessMessage();
        break;
        
      case PurchaseState.error:
        _hideLoadingDialog();
        setState(() {
          _isPurchasing = false;
          _purchasingProductId = null;
        });
        _showErrorMessage();
        break;
        
      case PurchaseState.canceled:
        _hideLoadingDialog();
        setState(() {
          _isPurchasing = false;
          _purchasingProductId = null;
        });
        _showCancelMessage();
        break;
        
      default:
        break;
    }
  }
  
  @override
  void dispose() {
    _shimmerController.dispose();
    _pulseController.dispose();
    _gemBalanceSubscription?.cancel();
    _purchaseStateSubscription?.cancel();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CluLuxuryTheme.vintageWhite,
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingView() : _buildMainContent(),
    );
  }
  
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () => Navigator.of(context).pop(),
        child: const Icon(
          CupertinoIcons.back,
          color: CluLuxuryTheme.deepDenim,
        ),
      ),
      title: const Text(
        'Gem Bazaar',
        style: TextStyle(
          color: CluLuxuryTheme.deepDenim,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
      actions: [
        _buildGemBalanceWidget(),
      ],
    );
  }
  
  Widget _buildGemBalanceWidget() {
    return Container(
      margin: const EdgeInsets.only(right: 16),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: CluLuxuryTheme.warmTaupe.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: CluLuxuryTheme.warmTaupe.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: CluLuxuryTheme.warmTaupe,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    CupertinoIcons.star_fill,
                    size: 10,
                    color: Colors.white,
                  ),
                ),
              );
            },
          ),
          const SizedBox(width: 6),
          Text(
            _currentGemBalance.toString(),
            style: const TextStyle(
              color: CluLuxuryTheme.deepDenim,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildLoadingView() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(CluLuxuryTheme.warmTaupe),
      ),
    );
  }
  
  Widget _buildMainContent() {
    return CustomScrollView(
      slivers: [
        // 头部横幅
        SliverToBoxAdapter(
          child: _buildHeaderBanner(),
        ),
        
        // 商品网格
        SliverPadding(
          padding: const EdgeInsets.all(16),
          sliver: SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.65, // 减小比例以增加高度
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) => _buildGoodsCard(_goodsList[index]),
              childCount: _goodsList.length,
            ),
          ),
        ),
        
        // 底部说明
        // SliverToBoxAdapter(
        //   child: _buildFooterInfo(),
        // ),
      ],
    );
  }
  
  Widget _buildHeaderBanner() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            CluLuxuryTheme.warmTaupe,
            CluLuxuryTheme.dustyRose,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: CluLuxuryTheme.warmTaupe.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Unlock Your Creativity',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Get gems to unlock premium features and enhance your crafting experience',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'Safe & Secure',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'Instant Delivery',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGoodsCard(CluSCGoods goods) {
    final isCurrentlyPurchasing = _isPurchasing && _purchasingProductId == goods.code;

    return CluLuxuryCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 商品头部
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: goods.isBigDeal
                  ? CluLuxuryTheme.dustyRose.withOpacity(0.1)
                  : CluLuxuryTheme.warmTaupe.withOpacity(0.05),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: Column(
              children: [
                // 宝石图标和数量
                AnimatedBuilder(
                  animation: _shimmerAnimation,
                  builder: (context, child) {
                    return Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            CluLuxuryTheme.warmTaupe,
                            CluLuxuryTheme.dustyRose,
                          ],
                          stops: [
                            (_shimmerAnimation.value + 1) / 2 - 0.1,
                            (_shimmerAnimation.value + 1) / 2 + 0.1,
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: CluLuxuryTheme.warmTaupe.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Icon(
                        CupertinoIcons.star_fill,
                        color: Colors.white,
                        size: 28,
                      ),
                    );
                  },
                ),
                const SizedBox(height: 8),
                Text(
                  '${goods.exchangeGem} Gems',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: CluLuxuryTheme.deepDenim,
                  ),
                ),
                const SizedBox(height: 4),
                // 标签区域 - 始终占位
                Container(
                  height: 16, // 固定高度确保对齐
                  child: goods.hasTag
                      ? Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: CluLuxuryTheme.dustyRose,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            goods.tags,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        )
                      : const SizedBox(), // 空占位
                ),
              ],
            ),
          ),

          // 商品信息
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const Spacer(),

                  // 价格显示
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (goods.isBigDeal) ...[
                        // 为Big Deal商品显示原价（划线）
                        Text(
                          _getOriginalPrice(goods),
                          style: const TextStyle(
                            fontSize: 12,
                            color: CluLuxuryTheme.oliveDrab,
                            decoration: TextDecoration.lineThrough,
                          ),
                        ),
                        const SizedBox(width: 4),
                      ],
                      Text(
                        '\$${goods.priceAmount.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: goods.isBigDeal
                              ? CluLuxuryTheme.dustyRose
                              : CluLuxuryTheme.deepDenim,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // 购买按钮
                  SizedBox(
                    height: 36,
                    child: CluLuxuryButton(
                      text: isCurrentlyPurchasing ? 'Processing...' : 'Purchase',
                      onPressed: isCurrentlyPurchasing ? null : () => _purchaseGoods(goods),
                      width: double.infinity,
                      size: CluButtonSize.small,
                      icon: isCurrentlyPurchasing
                          ? CupertinoIcons.clock
                          : CupertinoIcons.creditcard,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 计算Big Deal商品的原价（用于显示划线效果）
  String _getOriginalPrice(CluSCGoods goods) {
    if (!goods.isBigDeal) return '';

    // 根据商品代码计算原价（模拟折扣前价格）
    final currentPrice = goods.priceAmount;
    double originalPrice;

    switch (goods.code) {
      case "401009": // 500 gems for $1.99, 原价可能是 $4.99
        originalPrice = 4.99;
        break;
      case "401010": // 1200 gems for $4.99, 原价可能是 $9.99
        originalPrice = 9.99;
        break;
      case "401011": // 2500 gems for $11.99, 原价可能是 $19.99
        originalPrice = 19.99;
        break;
      case "401012": // 2600 gems for $12.99, 原价可能是 $19.99
        originalPrice = 19.99;
        break;
      case "401013": // 7000 gems for $34.99, 原价可能是 $49.99
        originalPrice = 49.99;
        break;
      case "401014": // 15000 gems for $79.99, 原价可能是 $99.99
        originalPrice = 99.99;
        break;
      case "401015": // 18000 gems for $99.99, 原价可能是 $129.99
        originalPrice = 129.99;
        break;
      default:
        // 默认增加30%作为原价
        originalPrice = currentPrice * 1.3;
    }

    return '\$${originalPrice.toStringAsFixed(2)}';
  }

  // Widget _buildFooterInfo() {
  //   return Container(
  //     margin: const EdgeInsets.all(16),
  //     padding: const EdgeInsets.all(16),
  //     decoration: BoxDecoration(
  //       color: CluLuxuryTheme.oliveDrab.withOpacity(0.05),
  //       borderRadius: BorderRadius.circular(12),
  //       border: Border.all(
  //         color: CluLuxuryTheme.oliveDrab.withOpacity(0.1),
  //         width: 1,
  //       ),
  //     ),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         const Text(
  //           'Purchase Information',
  //           style: TextStyle(
  //             fontSize: 16,
  //             fontWeight: FontWeight.w600,
  //             color: CluLuxuryTheme.deepDenim,
  //           ),
  //         ),
  //         const SizedBox(height: 8),
  //         const Text(
  //           '• All purchases are processed securely through Apple App Store\n'
  //           '• Gems are delivered instantly after successful payment\n'
  //           '• Purchases are non-refundable as per App Store policy\n'
  //           '• Contact support if you experience any issues',
  //           style: TextStyle(
  //             fontSize: 12,
  //             color: CluLuxuryTheme.oliveDrab,
  //             height: 1.4,
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  // 购买商品
  Future<void> _purchaseGoods(CluSCGoods goods) async {
    if (_isPurchasing) return;

    HapticFeedback.lightImpact();

    setState(() {
      _purchasingProductId = goods.code;
    });

    try {
      final success = await CluPurchaseVaultManager.instance.purchaseProduct(goods.code);
      if (!success) {
        setState(() {
          _purchasingProductId = null;
        });
      }
    } catch (e) {
      debugPrint('Purchase goods failed: $e');
      setState(() {
        _purchasingProductId = null;
      });
      _showErrorMessage();
    }
  }

  // 显示加载对话框
  void _showLoadingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const CupertinoAlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CupertinoActivityIndicator(),
            SizedBox(height: 16),
            Text('Processing purchase...'),
          ],
        ),
      ),
    );
  }

  // 隐藏加载对话框
  void _hideLoadingDialog() {
    if (Navigator.canPop(context)) {
      Navigator.of(context).pop();
    }
  }

  // 显示成功消息
  void _showSuccessMessage() {
    _showMessage(
      title: 'Purchase Successful',
      message: 'Gems have been added to your account!',
      isSuccess: true,
    );
  }

  // 显示错误消息
  void _showErrorMessage() {
    _showMessage(
      title: 'Purchase Failed',
      message: 'Unable to complete purchase. Please try again.',
      isSuccess: false,
    );
  }

  // 显示取消消息
  void _showCancelMessage() {
    _showMessage(
      title: 'Purchase Canceled',
      message: 'Transaction was canceled by user.',
      isSuccess: false,
    );
  }

  // 显示消息对话框
  void _showMessage({
    required String title,
    required String message,
    required bool isSuccess,
  }) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

// 垃圾代码段1 - 动画辅助器
class _AnimationHelper {
  static double calculateShimmerOffset(double progress) {
    return (progress - 0.5) * 2;
  }

  static Color interpolateColor(Color start, Color end, double progress) {
    return Color.lerp(start, end, progress) ?? start;
  }
}

// 垃圾代码段2 - 布局计算器
class _LayoutCalculator {
  static double calculateCardHeight(double screenWidth) {
    return screenWidth * 0.4;
  }

  static int calculateGridColumns(double screenWidth) {
    return screenWidth > 600 ? 3 : 2;
  }
}

// 垃圾代码段3 - 价格格式化器
class _PriceFormatter {
  static String formatPrice(double price) {
    return '\$${price.toStringAsFixed(2)}';
  }

  static String formatDiscount(double rate) {
    return '${(rate * 100).toInt()}% OFF';
  }
}
