// Clupa全屏视频播放页面
// 支持横屏/竖屏全屏播放，带有进度条控制

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';

import '../ClothVault/clu_craft_piece_model.dart';
import '../WeaveRoutes/clu_luxury_theme.dart';
import '../ThreadStore/clu_block_manager.dart';

class CluFullscreenVideoView extends StatefulWidget {
  final CluCraftPiece craftPiece;

  const CluFullscreenVideoView({
    super.key,
    required this.craftPiece,
  });

  @override
  State<CluFullscreenVideoView> createState() => _CluFullscreenVideoViewState();
}

class _CluFullscreenVideoViewState extends State<CluFullscreenVideoView>
    with TickerProviderStateMixin {
  VideoPlayerController? _controller;
  bool _isInitialized = false;
  bool _isPlaying = false;
  bool _showControls = true;
  bool _isDragging = false;
  
  late AnimationController _controlsAnimationController;
  late Animation<double> _controlsOpacity;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeVideo();
    _hideControlsAfterDelay();
  }

  void _initializeAnimations() {
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _controlsOpacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controlsAnimationController,
      curve: Curves.easeInOut,
    ));
    
    _controlsAnimationController.forward();
  }

  Future<void> _initializeVideo() async {
    try {
      _controller = VideoPlayerController.asset(widget.craftPiece.videoUrl);
      await _controller!.initialize();
      
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
        
        // 自动播放
        _controller!.play();
        _isPlaying = true;
        
        // 监听播放状态
        _controller!.addListener(_videoListener);
      }
    } catch (e) {
      debugPrint('视频初始化失败: $e');
    }
  }

  void _videoListener() {
    if (!mounted) return;
    
    final isPlaying = _controller!.value.isPlaying;
    if (_isPlaying != isPlaying) {
      setState(() {
        _isPlaying = isPlaying;
      });
    }
  }

  void _togglePlayPause() {
    if (_controller == null || !_isInitialized) return;
    
    HapticFeedback.lightImpact();
    
    if (_isPlaying) {
      _controller!.pause();
    } else {
      _controller!.play();
    }
    
    _showControlsTemporarily();
  }

  void _showControlsTemporarily() {
    setState(() {
      _showControls = true;
    });
    _controlsAnimationController.forward();
    _hideControlsAfterDelay();
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });

    if (_showControls) {
      _controlsAnimationController.forward();
      _hideControlsAfterDelay();
    } else {
      _controlsAnimationController.reverse();
    }
  }

  void _hideControlsAfterDelay() {
    Future.delayed(const Duration(seconds: 5), () { // 延长显示时间到5秒
      if (mounted && !_isDragging) {
        setState(() {
          _showControls = false;
        });
        _controlsAnimationController.reverse();
      }
    });
  }

  void _onProgressChanged(double value) {
    if (_controller == null || !_isInitialized) return;

    final duration = _controller!.value.duration;
    final newPosition = Duration(
      milliseconds: (duration.inMilliseconds * value).round(),
    );
    _controller!.seekTo(newPosition);
  }

  void _onProgressDragStart() {
    setState(() {
      _isDragging = true;
    });
  }

  void _onProgressDragEnd() {
    setState(() {
      _isDragging = false;
    });
    _hideControlsAfterDelay();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  void dispose() {
    _controller?.removeListener(_videoListener);
    _controller?.dispose();
    _controlsAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: () {
          if (_showControls) {
            setState(() {
              _showControls = false;
            });
            _controlsAnimationController.reverse();
          } else {
            _showControlsTemporarily();
          }
        },
        child: GestureDetector(
          onTap: _toggleControls,
          child: Stack(
            children: [
              // 视频播放器
              Center(
                child: _isInitialized && _controller != null
                    ? AspectRatio(
                        aspectRatio: _controller!.value.aspectRatio,
                        child: VideoPlayer(_controller!),
                      )
                    : Container(
                        color: Colors.black,
                        child: widget.craftPiece.thumbnailUrl != null
                            ? Image.asset(
                                widget.craftPiece.thumbnailUrl!,
                                fit: BoxFit.cover,
                              )
                            : const Center(
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    CluLuxuryTheme.warmTaupe,
                                  ),
                                ),
                              ),
                      ),
              ),
            
            // 控制层
            AnimatedBuilder(
              animation: _controlsOpacity,
              builder: (context, child) {
                return Opacity(
                  opacity: _controlsOpacity.value,
                  child: _buildControls(),
                );
              },
            ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildControls() {
    return Stack(
      children: [
        // 顶部控制栏
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Container(
            height: 100,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withOpacity(0.7),
                  Colors.transparent,
                ],
              ),
            ),
            child: SafeArea(
              child: Row(
                children: [
                  // 返回按钮
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () {
                      _controller?.pause();
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.3),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        CupertinoIcons.back,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                  
                  const Spacer(),
                  
                  // 视频标题
                  Expanded(
                    flex: 3,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Text(
                        widget.craftPiece.title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          shadows: [
                            Shadow(
                              offset: Offset(0, 1),
                              blurRadius: 3,
                              color: Colors.black54,
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2, // 允许显示2行
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  
                  const Spacer(),
                  
                  // 更多按钮
                  IconButton(
                    onPressed: _showMoreOptions,
                    icon: const Icon(
                      CupertinoIcons.ellipsis,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        
        // 中央播放/暂停按钮 - 只在显示控制时出现
        if (_showControls)
          Center(
            child: GestureDetector(
              onTap: () {
                _togglePlayPause();
                // 阻止事件冒泡到外层的GestureDetector
              },
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _isPlaying ? CupertinoIcons.pause_fill : CupertinoIcons.play_fill,
                  color: Colors.white,
                  size: 40,
                ),
              ),
            ),
          ),
        
        // 底部进度控制栏
        _buildBottomControls(),
      ],
    );
  }

  Widget _buildBottomControls() {
    if (!_isInitialized || _controller == null) {
      return const SizedBox.shrink();
    }

    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
            colors: [
              Colors.black.withOpacity(0.7),
              Colors.transparent,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 进度条行
                Row(
                  children: [
                    // 当前时间
                    Text(
                      _formatDuration(_controller!.value.position),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),

                    const SizedBox(width: 8),

                    // 进度条
                    Expanded(
                      child: SliderTheme(
                        data: SliderTheme.of(context).copyWith(
                          activeTrackColor: CluLuxuryTheme.warmTaupe,
                          inactiveTrackColor: Colors.white.withOpacity(0.3),
                          thumbColor: CluLuxuryTheme.warmTaupe,
                          thumbShape: const RoundSliderThumbShape(
                            enabledThumbRadius: 6,
                          ),
                          overlayShape: const RoundSliderOverlayShape(
                            overlayRadius: 12,
                          ),
                          trackHeight: 3,
                        ),
                        child: Slider(
                          value: _controller!.value.position.inMilliseconds.toDouble().clamp(
                            0.0,
                            _controller!.value.duration.inMilliseconds.toDouble()
                          ),
                          min: 0,
                          max: _controller!.value.duration.inMilliseconds.toDouble(),
                          onChanged: (value) {
                            final progress = value / _controller!.value.duration.inMilliseconds.toDouble();
                            _onProgressChanged(progress);
                          },
                          onChangeStart: (_) => _onProgressDragStart(),
                          onChangeEnd: (_) => _onProgressDragEnd(),
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // 总时长
                    Text(
                      _formatDuration(_controller!.value.duration),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // 播放控制按钮
                IconButton(
                  onPressed: _togglePlayPause,
                  icon: Icon(
                    _isPlaying ? CupertinoIcons.pause_fill : CupertinoIcons.play_fill,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showMoreOptions() {
    HapticFeedback.mediumImpact();

    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: Text(widget.craftPiece.title),
        message: Text('Created by ${widget.craftPiece.creatorDisplayName}'),
        actions: [
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              _shareCraft();
            },
            child: const Text('Share'),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              _blockCraft();
            },
            child: const Text('Block'),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              _reportCraft();
            },
            isDestructiveAction: true,
            child: const Text('Report'),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              _saveCraft();
            },
            child: const Text('Save'),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
      ),
    );
  }

  Future<void> _blockCraft() async {
    try {
      await CluBlockManager.instance.blockCraft(
        craftId: widget.craftPiece.craftId,
        creatorId: widget.craftPiece.creatorVipId,
        creatorName: widget.craftPiece.creatorDisplayName,
        title: widget.craftPiece.title,
      );

      HapticFeedback.mediumImpact();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Video blocked successfully'),
          backgroundColor: CluLuxuryTheme.warmTaupe,
          behavior: SnackBarBehavior.floating,
        ),
      );

      // 返回主页并刷新
      Navigator.of(context).pop(true);

    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to block video'),
          backgroundColor: CluLuxuryTheme.dustyRose,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  Future<void> _reportCraft() async {
    try {
      await CluBlockManager.instance.reportCraft(
        craftId: widget.craftPiece.craftId,
        creatorId: widget.craftPiece.creatorVipId,
        creatorName: widget.craftPiece.creatorDisplayName,
        title: widget.craftPiece.title,
      );

      HapticFeedback.mediumImpact();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Video reported and blocked successfully'),
          backgroundColor: CluLuxuryTheme.dustyRose,
          behavior: SnackBarBehavior.floating,
        ),
      );

      // 返回主页并刷新
      Navigator.of(context).pop(true);

    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to report video'),
          backgroundColor: CluLuxuryTheme.dustyRose,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _shareCraft() {
    HapticFeedback.mediumImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share feature coming soon!')),
    );
  }

  void _saveCraft() {
    HapticFeedback.mediumImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Save feature coming soon!')),
    );
  }
}
