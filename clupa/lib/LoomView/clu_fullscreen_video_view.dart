// Clupa全屏视频播放页面
// 支持横屏/竖屏全屏播放，带有进度条控制

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import 'dart:async';
import 'dart:io';

import '../ClothVault/clu_craft_piece_model.dart';
import '../WeaveRoutes/clu_luxury_theme.dart';
import '../ThreadStore/clu_block_manager.dart';
import '../ThreadStore/clu_user_post_manager.dart';
import '../ThreadStore/clu_vip_config_manager.dart';

class CluFullscreenVideoView extends StatefulWidget {
  final CluCraftPiece craftPiece;

  const CluFullscreenVideoView({
    super.key,
    required this.craftPiece,
  });

  @override
  State<CluFullscreenVideoView> createState() => _CluFullscreenVideoViewState();
}

class _CluFullscreenVideoViewState extends State<CluFullscreenVideoView>
    with TickerProviderStateMixin {
  VideoPlayerController? _controller;
  bool _isInitialized = false;
  bool _isPlaying = false;
  bool _showControls = true;
  bool _isDragging = false;

  late AnimationController _controlsAnimationController;
  late Animation<double> _controlsOpacity;
  Timer? _progressTimer;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeVideo();
  }

  void _initializeAnimations() {
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _controlsOpacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controlsAnimationController,
      curve: Curves.easeInOut,
    ));
    
    _controlsAnimationController.forward();
  }

  Future<void> _initializeVideo() async {
    try {
      // 检查视频路径是否为空或无效
      if (widget.craftPiece.videoUrl.isEmpty) {
        debugPrint('视频路径为空，使用默认视频');
        _initializeDefaultVideo();
        return;
      }

      debugPrint('正在初始化视频: ${widget.craftPiece.videoUrl}');

      // 根据路径类型选择合适的控制器
      if (widget.craftPiece.videoUrl.startsWith('assets/')) {
        // Asset视频
        debugPrint('使用Asset视频控制器');
        _controller = VideoPlayerController.asset(widget.craftPiece.videoUrl);
      } else if (widget.craftPiece.videoUrl.startsWith('/')) {
        // 本地文件路径
        debugPrint('使用File视频控制器');
        final file = File(widget.craftPiece.videoUrl);

        // 检查文件是否存在
        if (await file.exists()) {
          debugPrint('文件存在，创建控制器');
          _controller = VideoPlayerController.file(file);
        } else {
          debugPrint('文件不存在: ${widget.craftPiece.videoUrl}');
          _initializeDefaultVideo();
          return;
        }
      } else {
        // 网络视频或其他情况，默认使用asset
        debugPrint('使用默认Asset视频');
        _controller = VideoPlayerController.asset('assets/av/video1.mp4');
      }

      debugPrint('开始初始化视频控制器');
      await _controller!.initialize();
      debugPrint('视频控制器初始化成功');

      if (mounted) {
        setState(() {
          _isInitialized = true;
        });

        // 自动播放
        _controller!.play();
        _isPlaying = true;
        _startProgressTimer();

        // 监听播放状态
        _controller!.addListener(_videoListener);
      }
    } catch (e) {
      debugPrint('视频初始化失败: $e');
      debugPrint('视频路径: ${widget.craftPiece.videoUrl}');
      // 如果初始化失败，尝试使用默认视频
      if (mounted) {
        _initializeDefaultVideo();
      }
    }
  }

  void _initializeDefaultVideo() async {
    try {
      debugPrint('初始化默认视频');
      _controller?.dispose();
      _controller = VideoPlayerController.asset('assets/av/video1.mp4');
      await _controller!.initialize();
      debugPrint('默认视频初始化成功');

      if (mounted) {
        setState(() {
          _isInitialized = true;
        });

        // 自动播放
        _controller!.play();
        _isPlaying = true;
        _startProgressTimer();

        // 监听播放状态
        _controller!.addListener(_videoListener);
      }
    } catch (e) {
      debugPrint('默认视频初始化也失败: $e');
      // 最后的fallback，设置一个错误状态
      if (mounted) {
        setState(() {
          _isInitialized = false;
        });
      }
    }
  }

  void _videoListener() {
    if (!mounted) return;
    
    final isPlaying = _controller!.value.isPlaying;
    if (_isPlaying != isPlaying) {
      setState(() {
        _isPlaying = isPlaying;
      });
    }
  }

  void _togglePlayPause() {
    if (_controller == null || !_isInitialized) return;

    HapticFeedback.lightImpact();

    setState(() {
      if (_isPlaying) {
        _controller!.pause();
        _isPlaying = false;
        _stopProgressTimer();
      } else {
        _controller!.play();
        _isPlaying = true;
        _startProgressTimer();
      }
    });
  }

  void _startProgressTimer() {
    _progressTimer?.cancel();
    _progressTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (mounted && _controller != null && _controller!.value.isInitialized && !_isDragging) {
        setState(() {
          // 触发进度条更新
        });
      }
    });
  }

  void _stopProgressTimer() {
    _progressTimer?.cancel();
    _progressTimer = null;
  }



  void _onProgressChanged(double value) {
    if (_controller == null || !_isInitialized) return;

    final duration = _controller!.value.duration;
    final newPosition = Duration(
      milliseconds: (duration.inMilliseconds * value).round(),
    );
    _controller!.seekTo(newPosition);
  }

  void _onProgressDragStart() {
    setState(() {
      _isDragging = true;
    });
  }

  void _onProgressDragEnd() {
    setState(() {
      _isDragging = false;
    });
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  void dispose() {
    _stopProgressTimer();
    _controller?.removeListener(_videoListener);
    _controller?.dispose();
    _controlsAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _togglePlayPause,
        child: Stack(
            children: [
              // 视频播放器
              Center(
                child: _isInitialized && _controller != null
                    ? AspectRatio(
                        aspectRatio: _controller!.value.aspectRatio,
                        child: VideoPlayer(_controller!),
                      )
                    : Container(
                        color: Colors.black,
                        child: widget.craftPiece.thumbnailUrl != null
                            ? Image.asset(
                                widget.craftPiece.thumbnailUrl!,
                                fit: BoxFit.cover,
                              )
                            : const Center(
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    CluLuxuryTheme.warmTaupe,
                                  ),
                                ),
                              ),
                      ),
              ),

            // 控制层
            AnimatedBuilder(
              animation: _controlsOpacity,
              builder: (context, child) {
                return Opacity(
                  opacity: _controlsOpacity.value,
                  child: _buildControls(),
                );
              },
            ),
            ],
          ),
        ),
      );
  }

  Widget _buildControls() {
    return Stack(
      children: [
        // 顶部控制栏
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withOpacity(0.7),
                  Colors.transparent,
                ],
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.fromLTRB(20, 40, 20, 20),
              child: Row(
                children: [
                  // 返回按钮
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(25),
                      onTap: () {
                        _controller?.pause();
                        Navigator.of(context).pop();
                      },
                      child: Container(
                        width: 50,
                        height: 50,
                        child: const Icon(
                          CupertinoIcons.chevron_left,
                          color: Colors.white,
                          size: 26,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // 视频标题
                  Expanded(
                    child: Text(
                      widget.craftPiece.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        shadows: [
                          Shadow(
                            offset: Offset(0, 1),
                            blurRadius: 3,
                            color: Colors.black54,
                          ),
                        ],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  const SizedBox(width: 16),

                  // 更多按钮
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(25),
                      onTap: _showMoreOptions,
                      child: Container(
                        width: 50,
                        height: 50,
                        child: const Icon(
                          CupertinoIcons.ellipsis_vertical_circle,
                          color: Colors.white,
                          size: 26,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        
        // 中央播放/暂停按钮 - 暂停时显示
        if (!_isPlaying)
          Center(
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withOpacity(0.95),
                border: Border.all(
                  color: CluLuxuryTheme.warmTaupe.withOpacity(0.3),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 20,
                    offset: const Offset(0, 4),
                  ),
                  BoxShadow(
                    color: Colors.white.withOpacity(0.8),
                    blurRadius: 10,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Icon(
                _isPlaying ? CupertinoIcons.pause_fill : CupertinoIcons.play_fill,
                color: CluLuxuryTheme.deepDenim,
                size: 30,
              ),
            ),
          ),
        
        // 底部进度控制栏
        _buildBottomControls(),
      ],
    );
  }

  Widget _buildBottomControls() {
    if (!_isInitialized || _controller == null) {
      return const SizedBox.shrink();
    }

    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
            colors: [
              Colors.black.withOpacity(0.7),
              Colors.transparent,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 进度条行
                Row(
                  children: [
                    // 当前时间
                    Text(
                      _formatDuration(_controller!.value.position),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),

                    const SizedBox(width: 8),

                    // 进度条
                    Expanded(
                      child: SliderTheme(
                        data: SliderTheme.of(context).copyWith(
                          activeTrackColor: CluLuxuryTheme.warmTaupe,
                          inactiveTrackColor: Colors.white.withOpacity(0.3),
                          thumbColor: CluLuxuryTheme.warmTaupe,
                          thumbShape: const RoundSliderThumbShape(
                            enabledThumbRadius: 6,
                          ),
                          overlayShape: const RoundSliderOverlayShape(
                            overlayRadius: 12,
                          ),
                          trackHeight: 3,
                        ),
                        child: Slider(
                          value: _controller!.value.position.inMilliseconds.toDouble().clamp(
                            0.0,
                            _controller!.value.duration.inMilliseconds.toDouble()
                          ),
                          min: 0,
                          max: _controller!.value.duration.inMilliseconds.toDouble(),
                          onChanged: (value) {
                            final progress = value / _controller!.value.duration.inMilliseconds.toDouble();
                            _onProgressChanged(progress);
                          },
                          onChangeStart: (_) => _onProgressDragStart(),
                          onChangeEnd: (_) => _onProgressDragEnd(),
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // 总时长
                    Text(
                      _formatDuration(_controller!.value.duration),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 检查是否为用户自己发布的视频
  bool _isUserOwnVideo() {
    // 检查是否是用户帖子
    final isUserPost = widget.craftPiece.craftId.startsWith('user_post_') ||
                      (widget.craftPiece.toJson()['isUserPost'] == true);
    return isUserPost;
  }

  void _showMoreOptions() {
    HapticFeedback.mediumImpact();

    final isOwnVideo = _isUserOwnVideo();

    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: Text(widget.craftPiece.title),
        message: Text(isOwnVideo
            ? 'Your video'
            : 'Created by ${widget.craftPiece.creatorDisplayName}'),
        actions: isOwnVideo ? [
          // 用户自己的视频显示删除选项
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              _deleteVideo();
            },
            isDestructiveAction: true,
            child: const Text('Delete'),
          ),
        ] : [
          // 其他用户的视频显示常规选项
          // CupertinoActionSheetAction(
          //   onPressed: () {
          //     Navigator.pop(context);
          //     _shareCraft();
          //   },
          //   child: const Text('Share'),
          // ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              _blockCraft();
            },
            child: const Text('Block'),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              _reportCraft();
            },
            isDestructiveAction: true,
            child: const Text('Report'),
          ),
          // CupertinoActionSheetAction(
          //   onPressed: () {
          //     Navigator.pop(context);
          //     _saveCraft();
          //   },
          //   child: const Text('Save'),
          // ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
      ),
    );
  }

  Future<void> _blockCraft() async {
    try {
      await CluBlockManager.instance.blockCraft(
        craftId: widget.craftPiece.craftId,
        creatorId: widget.craftPiece.creatorVipId,
        creatorName: widget.craftPiece.creatorDisplayName,
        title: widget.craftPiece.title,
        videoUrl: widget.craftPiece.videoUrl,
      );

      HapticFeedback.mediumImpact();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Video blocked successfully'),
          backgroundColor: CluLuxuryTheme.warmTaupe,
          behavior: SnackBarBehavior.floating,
        ),
      );

      // 返回主页并刷新
      Navigator.of(context).pop(true);

    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to block video'),
          backgroundColor: CluLuxuryTheme.dustyRose,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  Future<void> _reportCraft() async {
    // 显示举报选项对话框
    final reportReason = await showCupertinoModalPopup<String>(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: const Text('Report Video'),
        message: const Text('Why are you reporting this video?'),
        actions: [
          CupertinoActionSheetAction(
            onPressed: () => Navigator.of(context).pop('inappropriate'),
            child: const Text('Inappropriate Content'),
          ),
          CupertinoActionSheetAction(
            onPressed: () => Navigator.of(context).pop('spam'),
            child: const Text('Spam'),
          ),
          CupertinoActionSheetAction(
            onPressed: () => Navigator.of(context).pop('harassment'),
            child: const Text('Harassment'),
          ),
          CupertinoActionSheetAction(
            onPressed: () => Navigator.of(context).pop('copyright'),
            child: const Text('Copyright Violation'),
          ),
          CupertinoActionSheetAction(
            onPressed: () => Navigator.of(context).pop('other'),
            child: const Text('Other'),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ),
    );

    if (reportReason != null) {
      try {
        await CluBlockManager.instance.reportCraft(
          craftId: widget.craftPiece.craftId,
          creatorId: widget.craftPiece.creatorVipId,
          creatorName: widget.craftPiece.creatorDisplayName,
          title: widget.craftPiece.title,
          videoUrl: widget.craftPiece.videoUrl,
        );

        HapticFeedback.mediumImpact();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Video reported and blocked successfully'),
            backgroundColor: CluLuxuryTheme.dustyRose,
            behavior: SnackBarBehavior.floating,
          ),
        );

        // 返回主页并刷新
        Navigator.of(context).pop(true);

      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to report video'),
            backgroundColor: CluLuxuryTheme.dustyRose,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _shareCraft() {
    HapticFeedback.mediumImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share feature coming soon!')),
    );
  }

  void _saveCraft() {
    HapticFeedback.mediumImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Save feature coming soon!')),
    );
  }

  Future<void> _deleteVideo() async {
    HapticFeedback.mediumImpact();

    // 显示确认对话框
    final shouldDelete = await showCupertinoDialog<bool>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Delete Video'),
        content: const Text('Are you sure you want to delete this video? This action cannot be undone.'),
        actions: [
          CupertinoDialogAction(
            child: const Text('Cancel'),
            onPressed: () => Navigator.of(context).pop(false),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            child: const Text('Delete'),
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      ),
    );

    if (shouldDelete == true) {
      try {
        // 删除用户帖子
        final success = await CluUserPostManager.instance.deletePost(widget.craftPiece.craftId);

        if (success) {
          HapticFeedback.mediumImpact();

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Video deleted successfully'),
              backgroundColor: CluLuxuryTheme.warmTaupe,
              behavior: SnackBarBehavior.floating,
            ),
          );

          // 返回主页并刷新，传递删除成功的标志
          Navigator.of(context).pop('deleted');
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to delete video'),
              backgroundColor: CluLuxuryTheme.dustyRose,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to delete video'),
            backgroundColor: CluLuxuryTheme.dustyRose,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}
