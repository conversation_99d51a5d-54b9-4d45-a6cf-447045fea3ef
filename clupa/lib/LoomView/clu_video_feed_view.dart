// Clupa视频流页面 - VideoFeed
// 竖屏视频流，展示拼布作品视频

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:video_player/video_player.dart';

import '../ThreadStore/clu_app_state.dart';
import '../StitchMaster/clu_needle_actions.dart';
import '../ClothVault/clu_craft_piece_model.dart';
import '../ClothVault/clu_user_post_model.dart';
import '../ThreadStore/clu_user_post_manager.dart';
import '../WeaveRoutes/clu_luxury_theme.dart';
import '../FiberParts/clu_luxury_button.dart';
import 'clu_video_player_widget.dart';
import 'clu_fullscreen_video_view.dart';
import 'clu_chat_conversation_view.dart';
import 'clu_block_management_view.dart';
import '../ThreadStore/clu_block_manager.dart';
import '../ThreadStore/clu_like_manager.dart';
import '../ThreadStore/clu_thumbnail_manager.dart';

class CluVideoFeedView extends StatefulWidget {
  const CluVideoFeedView({super.key});

  @override
  State<CluVideoFeedView> createState() => _CluVideoFeedViewState();
}

class _CluVideoFeedViewState extends State<CluVideoFeedView>
    with AutomaticKeepAliveClientMixin {
  late PageController _pageController;
  int _currentVideoIndex = 0;
  CluVideoPlayerWidget? _currentVideoPlayer;
  bool _shouldAutoPlay = true; // 控制视频是否应该自动播放

  List<CluCraftPiece> _allVideos = []; // 合并后的所有视频数据
  List<CluUserPost> _userPosts = []; // 用户帖子

  // 预设视频数据（使用本地视频文件）
  final List<CluCraftPiece> _mockVideos = [
    CluCraftPiece(
      craftId: 'craft_001',
      creatorVipId: 'vip_001',
      creatorDisplayName: 'Emma Quilter',
      creatorAvatarUrl: 'assets/avatars/clu_avatar_01.jpg',
      title: 'Transform Your Jeans',
      description: "Easy Upcycle Idea. I'm making a hobo bag with old jeans. I'm working on a more detailed tutorial for this one.",
      videoUrl: 'assets/av/video1.mp4',
      thumbnailUrl: 'assets/patchwork/clu_patchwork_01.jpg',
      craftStyle: 'denimupcycle',
      difficultyLevel: 'patchwork',
      estimatedHours: 2,
      techniquesUsed: ['Piecing', 'Hand Quilting'],
      textileTypes: ['Cotton', 'Linen'],
      likesCount: 1247,
      commentsCount: 89,
      viewsCount: 5632,
      createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      isPublished: true,
    ),
    CluCraftPiece(
      craftId: 'craft_002',
      creatorVipId: 'vip_002',
      creatorDisplayName: 'Sarah Stitches',
      creatorAvatarUrl: 'assets/avatars/clu_avatar_02.jpg',
      title: 'Almofada com tecido jeans',
      description: '👖😍✂️🪡🧵💡',
      videoUrl: 'assets/av/video2.mp4',
      thumbnailUrl: 'assets/patchwork/clu_patchwork_02.jpg',
      craftStyle: 'costura',
      difficultyLevel: 'costurice',
      estimatedHours: 4,
      techniquesUsed: ['Machine Piecing', 'Applique'],
      textileTypes: ['Denim', 'Cotton'],
      likesCount: 892,
      commentsCount: 56,
      viewsCount: 3421,
      createdAt: DateTime.now().subtract(const Duration(hours: 5)),
      isPublished: true,
    ),
    CluCraftPiece(
      craftId: 'craft_003',
      creatorVipId: 'vip_003',
      creatorDisplayName: 'Maya Fabric',
      creatorAvatarUrl: 'assets/avatars/clu_avatar_03.jpg',
      title: 'Rhombus triangles know how to party',
      description: 'Block 28 of the Dilly Dally quilt is giving sparkle with just the basics.',
      videoUrl: 'assets/av/video3.mp4',
      thumbnailUrl: 'assets/patchwork/clu_patchwork_03.jpg',
      craftStyle: 'Art Quilt',
      difficultyLevel: 'Advanced',
      estimatedHours: 20,
      techniquesUsed: ['Free Motion Quilting', 'Embroidery', 'Beading'],
      textileTypes: ['Silk', 'Wool', 'Cotton'],
      likesCount: 2156,
      commentsCount: 134,
      viewsCount: 8945,
      createdAt: DateTime.now().subtract(const Duration(hours: 8)),
      isPublished: true,
    ),
    // 复用视频，创建更多数据
    CluCraftPiece(
      craftId: 'craft_004',
      creatorVipId: 'vip_004',
      creatorDisplayName: 'Lisa Patches',
      creatorAvatarUrl: 'assets/avatars/clu_avatar_04.jpg',
      title: 'Cosiest backing fabric',
      description: 'We have pieced together the exquisite baby clothes of our beloved ones to create a warm and touching commemorative blanket.',
      videoUrl: 'assets/av/video4.mp4', // 复用video1
      thumbnailUrl: 'assets/patchwork/clu_patchwork_04.jpg',
      craftStyle: 'keep sake blanket',
      difficultyLevel: 'memories',
      estimatedHours: 12,
      techniquesUsed: ['T-shirt Quilting', 'Stabilizing'],
      textileTypes: ['Cotton Jersey', 'Interfacing'],
      likesCount: 756,
      commentsCount: 42,
      viewsCount: 2890,
      createdAt: DateTime.now().subtract(const Duration(hours: 12)),
      isPublished: true,
    ),
    CluCraftPiece(
      craftId: 'craft_005',
      creatorVipId: 'vip_005',
      creatorDisplayName: 'Anna Threads',
      creatorAvatarUrl: 'assets/avatars/clu_avatar_05.jpg',
      title: 'patchwork quilt',
      description: 'A quilt i’ll treasure forever🥹♥️ ',
      videoUrl: 'assets/av/video5.mp4', // 复用video2
      thumbnailUrl: 'assets/patchwork/clu_patchwork_05.jpg',
      craftStyle: 'memoryblanket',
      difficultyLevel: 'patchworkquilt',
      estimatedHours: 15,
      techniquesUsed: ['Boro', 'Sashiko', 'Hand Stitching'],
      textileTypes: ['Indigo Cotton', 'Linen'],
      likesCount: 1834,
      commentsCount: 98,
      viewsCount: 6754,
      createdAt: DateTime.now().subtract(const Duration(hours: 18)),
      isPublished: true,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _initializeBlockManager();
    _initializeLikeManager();
    _loadUserPosts();

    // 监听用户帖子变化
    CluUserPostManager.instance.postsStream.listen((posts) {
      if (mounted) {
        debugPrint('用户帖子流更新，数量: ${posts.length}');
        final oldCount = _userPosts.length;
        setState(() {
          _userPosts = posts;
          _updateAllVideos();
        });

        // 立即更新Redux状态
        final store = StoreProvider.of<CluAppState>(context);
        store.dispatch(CluLoadCommunityFeedSuccessAction(
          feedItems: List.from(_allVideos),
          page: 0,
        ));

        // 如果有新帖子添加，滚动到顶部
        if (posts.length > oldCount) {
          Future.delayed(const Duration(milliseconds: 300), () {
            scrollToTop();
          });
        }
      }
    });

    // 加载社区视频流
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCommunityFeed();
    });
  }

  Future<void> _initializeBlockManager() async {
    await CluBlockManager.instance.initialize();
  }

  Future<void> _initializeLikeManager() async {
    await CluLikeManager.instance.initialize();

    // 初始化预设视频的点赞数
    final initialCounts = <String, int>{};
    for (final video in _mockVideos) {
      initialCounts[video.craftId] = video.likesCount;
    }
    await CluLikeManager.instance.initializeCraftLikes(initialCounts);

    // 预加载预设视频的缩略图
    await _preloadThumbnails();
  }

  Future<void> _preloadThumbnails() async {
    try {
      final assetPaths = _mockVideos.map((video) => video.videoUrl).toList();
      await CluThumbnailManager.instance.preloadAssetThumbnails(assetPaths);
      debugPrint('预设视频缩略图预加载完成');
    } catch (e) {
      debugPrint('预加载缩略图失败: $e');
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  // 加载用户帖子
  Future<void> _loadUserPosts() async {
    try {
      await CluUserPostManager.instance.initialize();
      final userPosts = CluUserPostManager.instance.currentPosts;

      debugPrint('加载用户帖子数量: ${userPosts.length}');

      if (mounted) {
        setState(() {
          _userPosts = userPosts;
          _updateAllVideos();
        });
      }
    } catch (e) {
      debugPrint('Failed to load user posts: $e');
    }
  }

  // 更新合并后的视频列表
  void _updateAllVideos() {
    final newAllVideos = <CluCraftPiece>[];

    // 先添加用户帖子（转换为CluCraftPiece格式）
    for (final post in _userPosts) {
      try {
        final craftPiece = post.toCraftPieceData();
        newAllVideos.add(craftPiece);
        debugPrint('添加用户帖子: ${post.title}, 视频路径: ${post.videoPath}');
      } catch (e) {
        debugPrint('转换用户帖子失败: $e');
      }
    }

    // 再添加预设视频
    newAllVideos.addAll(_mockVideos);

    _allVideos = newAllVideos;
    debugPrint('合并后的视频总数: ${_allVideos.length}');
  }

  void _loadCommunityFeed() {
    final store = StoreProvider.of<CluAppState>(context);
    store.dispatch(CluLoadCommunityFeedAction(page: 0));

    // 重新加载用户帖子，确保获取最新数据
    _loadUserPosts().then((_) {
      debugPrint('用户帖子加载完成，开始更新Redux状态');
      // 模拟加载成功，使用最新的合并数据
      Future.delayed(const Duration(milliseconds: 200), () {
        if (mounted) {
          store.dispatch(CluLoadCommunityFeedSuccessAction(
            feedItems: List.from(_allVideos), // 创建新的列表副本
            page: 0,
          ));
          debugPrint('Redux状态已更新，视频数量: ${_allVideos.length}');
        }
      });
    });
  }

  void _onVideoChanged(int index) {
    setState(() {
      _currentVideoIndex = index;
      _shouldAutoPlay = true; // 切换视频时恢复自动播放
    });
    HapticFeedback.selectionClick();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return StoreConnector<CluAppState, _ViewModel>(
      converter: _ViewModel.fromStore,
      onWillChange: (oldViewModel, newViewModel) {
        // 当社区Feed被刷新时，滚动到顶部
        if (oldViewModel != null &&
            newViewModel != null &&
            oldViewModel.communityFeed.length != newViewModel.communityFeed.length) {
          Future.delayed(const Duration(milliseconds: 300), () {
            scrollToTop();
          });
        }
      },
      builder: (context, viewModel) {
        if (viewModel.isLoading) {
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(CluLuxuryTheme.warmTaupe),
            ),
          );
        }

        // 优先使用Redux中的最新数据
        List<CluCraftPiece> allVideos;
        if (viewModel.communityFeed.isNotEmpty) {
          allVideos = viewModel.communityFeed;
          debugPrint('使用Redux中的视频数据，总数: ${allVideos.length}');
        } else if (_allVideos.isNotEmpty) {
          allVideos = _allVideos;
          debugPrint('使用合并后的视频数据，总数: ${allVideos.length}');
        } else {
          allVideos = _mockVideos;
          debugPrint('使用预设视频数据，总数: ${allVideos.length}');
        }

        // 过滤被屏蔽的视频
        final blockedIds = CluBlockManager.instance.getBlockedCraftIds();
        final videos = allVideos.where((video) =>
            !blockedIds.contains(video.craftId)
        ).toList();

        debugPrint('过滤后的视频数量: ${videos.length}');

        if (videos.isEmpty) {
          return _buildEmptyState();
        }

        return Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: CluLuxuryTheme.vintageWhite,
            elevation: 0,
            systemOverlayStyle: const SystemUiOverlayStyle(
              statusBarColor: CluLuxuryTheme.vintageWhite,
              statusBarIconBrightness: Brightness.dark,
              statusBarBrightness: Brightness.light,
            ),
            title: const Text(
              'Clupa',
              style: TextStyle(
                color: CluLuxuryTheme.deepDenim,
                fontSize: 20,
                fontWeight: FontWeight.w700,
                decoration: TextDecoration.none,
              ),
            ),
            centerTitle: false,
            automaticallyImplyLeading: false,
          ),
          body: PageView.builder(
            controller: _pageController,
            scrollDirection: Axis.vertical,
            onPageChanged: _onVideoChanged,
            itemCount: videos.length,
            itemBuilder: (context, index) {
              // 获取更新后的点赞信息
              final craft = _getUpdatedCraftPiece(videos[index]);

              final videoPlayer = CluVideoPlayerWidget(
                key: ValueKey(videos[index].craftId), // 添加唯一key
                craftPiece: craft,
                isActive: index == _currentVideoIndex && _shouldAutoPlay,
                onLike: () => _handleLikeCraft(videos[index]),
                onFullscreen: () => _openFullscreenVideo(videos[index]),
                onMore: () => _showMoreOptions(videos[index]),
                onProfile: () => _showCreatorProfile(videos[index]),
              );

              // 保存当前视频播放器的引用
              if (index == _currentVideoIndex) {
                _currentVideoPlayer = videoPlayer;
              }

              return videoPlayer;
            },
          ),
        );
      },
    );
  }

  // 获取包含最新点赞信息的作品数据
  CluCraftPiece _getUpdatedCraftPiece(CluCraftPiece originalCraft) {
    // 检查是否是用户自己的帖子
    final isUserPost = originalCraft.craftId.startsWith('user_post_') ||
                      (originalCraft.toJson()['isUserPost'] == true);

    if (isUserPost) {
      // 用户帖子，从用户帖子管理器获取最新状态
      final userPost = CluUserPostManager.instance.getPostById(originalCraft.craftId);
      if (userPost != null) {
        return originalCraft.copyWith(
          isLikedByCurrentVip: userPost.isLiked,
          likesCount: userPost.likeCount,
        );
      }
    } else {
      // 其他视频，从点赞管理器获取最新状态
      final isLiked = CluLikeManager.instance.isLiked(originalCraft.craftId);
      final likeCount = CluLikeManager.instance.getLikeCount(originalCraft.craftId, originalCraft.likesCount);

      return originalCraft.copyWith(
        isLikedByCurrentVip: isLiked,
        likesCount: likeCount,
      );
    }

    return originalCraft;
  }

  Future<void> _handleLikeCraft(CluCraftPiece craft) async {
    try {
      // 检查是否是用户自己的帖子
      final isUserPost = craft.craftId.startsWith('user_post_') ||
                        (craft.toJson()['isUserPost'] == true);

      if (isUserPost) {
        // 用户自己的帖子，使用用户帖子管理器
        await CluUserPostManager.instance.toggleLike(craft.craftId);
      } else {
        // 其他视频，使用点赞管理器
        await CluLikeManager.instance.toggleLike(craft.craftId, craft.likesCount);
      }

      // 刷新UI
      setState(() {});
    } catch (e) {
      debugPrint('Failed to handle like: $e');
    }
  }

  void _openFullscreenVideo(CluCraftPiece craft) {
    HapticFeedback.lightImpact();

    // 暂停当前视频
    _pauseCurrentVideo();

    // 导航到全屏播放页面
    Navigator.of(context).push(
      CupertinoPageRoute(
        builder: (context) => CluFullscreenVideoView(craftPiece: craft),
        fullscreenDialog: true,
      ),
    ).then((result) {
      // 返回时恢复播放
      _resumeCurrentVideo();

      // 如果从全屏页面屏蔽了视频，只刷新列表不切换视频
      if (result == true) {
        setState(() {});
      }
      // 如果删除了视频，需要切换到下一个视频
      else if (result == 'deleted') {
        _handleVideoDeleted();
      }
    });
  }

  void _showMoreOptions(CluCraftPiece craft) {
    HapticFeedback.mediumImpact();

    // 检查是否是用户帖子
    final isUserPost = craft.craftId.startsWith('user_post_') ||
                      (craft.toJson()['isUserPost'] == true);

    final actions = <CupertinoActionSheetAction>[];

    // 用户帖子显示删除选项
    if (isUserPost) {
      actions.add(
        CupertinoActionSheetAction(
          onPressed: () {
            Navigator.pop(context);
            _deleteUserPost(craft);
          },
          isDestructiveAction: true,
          child: const Text('Delete'),
        ),
      );
    } else {
      // 非用户帖子显示常规选项
      actions.addAll([
        // CupertinoActionSheetAction(
        //   onPressed: () {
        //     Navigator.pop(context);
        //     _shareCraft(craft);
        //   },
        //   child: const Text('Share'),
        // ),
        CupertinoActionSheetAction(
          onPressed: () {
            Navigator.pop(context);
            _blockCraft(craft);
          },
          child: const Text('Block'),
        ),
        CupertinoActionSheetAction(
          onPressed: () {
            Navigator.pop(context);
            _reportCraft(craft);
          },
          isDestructiveAction: true,
          child: const Text('Report'),
        ),
        // CupertinoActionSheetAction(
        //   onPressed: () {
        //     Navigator.pop(context);
        //     _saveCraft(craft);
        //   },
        //   child: const Text('Save'),
        // ),
      ]);
    }

    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: Text(craft.title),
        message: Text('Created by ${craft.creatorDisplayName}'),
        actions: actions,
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      color: Colors.black,
      child: SafeArea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: CluLuxuryTheme.warmTaupe.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  CupertinoIcons.video_camera_solid,
                  color: CluLuxuryTheme.warmTaupe,
                  size: 50,
                ),
              ),

              const SizedBox(height: 24),

              const Text(
                'No Videos Available',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),

              const SizedBox(height: 12),

              const Text(
                'All videos have been blocked or\nthere are no videos to show',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white70,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 32),

              CupertinoButton.filled(
                onPressed: () {
                  _navigateToBlockManagement();
                },
                child: const Text(
                  'Manage Blocked Content',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _blockCraft(CluCraftPiece craft) async {
    try {
      await CluBlockManager.instance.blockCraft(
        craftId: craft.craftId,
        creatorId: craft.creatorVipId,
        creatorName: craft.creatorDisplayName,
        title: craft.title,
        videoUrl: craft.videoUrl,
      );

      HapticFeedback.mediumImpact();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Video blocked successfully'),
          backgroundColor: CluLuxuryTheme.warmTaupe,
          behavior: SnackBarBehavior.floating,
        ),
      );

      // 刷新列表以过滤被屏蔽的视频
      setState(() {});

    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to block video'),
          backgroundColor: CluLuxuryTheme.dustyRose,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  Future<void> _reportCraft(CluCraftPiece craft) async {
    // 显示举报选项对话框
    final reportReason = await showCupertinoModalPopup<String>(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: const Text('Report Video'),
        message: const Text('Why are you reporting this video?'),
        actions: [
          CupertinoActionSheetAction(
            onPressed: () => Navigator.of(context).pop('inappropriate'),
            child: const Text('Inappropriate Content'),
          ),
          CupertinoActionSheetAction(
            onPressed: () => Navigator.of(context).pop('spam'),
            child: const Text('Spam'),
          ),
          CupertinoActionSheetAction(
            onPressed: () => Navigator.of(context).pop('harassment'),
            child: const Text('Harassment'),
          ),
          CupertinoActionSheetAction(
            onPressed: () => Navigator.of(context).pop('copyright'),
            child: const Text('Copyright Violation'),
          ),
          CupertinoActionSheetAction(
            onPressed: () => Navigator.of(context).pop('other'),
            child: const Text('Other'),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ),
    );

    if (reportReason != null) {
      try {
        await CluBlockManager.instance.reportCraft(
          craftId: craft.craftId,
          creatorId: craft.creatorVipId,
          creatorName: craft.creatorDisplayName,
          title: craft.title,
          videoUrl: craft.videoUrl,
        );

        HapticFeedback.mediumImpact();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Video reported and blocked successfully'),
            backgroundColor: CluLuxuryTheme.dustyRose,
            behavior: SnackBarBehavior.floating,
          ),
        );

        // 刷新列表以过滤被举报的视频
        setState(() {});

      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to report video'),
            backgroundColor: CluLuxuryTheme.dustyRose,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _shareCraft(CluCraftPiece craft) {
    HapticFeedback.mediumImpact();
    // TODO: 实现分享功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share feature coming soon!')),
    );
  }

  void _saveCraft(CluCraftPiece craft) {
    HapticFeedback.mediumImpact();
    // TODO: 实现收藏功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Save feature coming soon!')),
    );
  }

  void _pauseCurrentVideo() {
    // 暂停当前播放的视频
    setState(() {
      _shouldAutoPlay = false;
    });
  }

  void _resumeCurrentVideo() {
    // 恢复当前视频播放
    setState(() {
      _shouldAutoPlay = true;
    });
  }

  void _showCreatorProfile(CluCraftPiece craft) {
    HapticFeedback.lightImpact();

    // 导航到聊天页面
    Navigator.of(context).push(
      CupertinoPageRoute(
        builder: (context) => CluChatConversationView(
          participantId: craft.creatorVipId,
          participantName: craft.creatorDisplayName,
          participantAvatar: craft.creatorAvatarUrl ?? '',
        ),
      ),
    );
  }

  void _moveToNextVideo() {
    // 强制重建以应用过滤
    setState(() {});

    // 延迟一帧后检查是否需要跳转
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_pageController.hasClients) {
        final currentPage = _pageController.page?.round() ?? 0;

        // 获取过滤后的视频列表长度
        final store = StoreProvider.of<CluAppState>(context, listen: false);
        final allVideos = store.state.communityFeed.isNotEmpty
            ? store.state.communityFeed
            : _mockVideos;
        final blockedIds = CluBlockManager.instance.getBlockedCraftIds();
        final filteredVideos = allVideos.where((video) =>
            !blockedIds.contains(video.craftId)
        ).toList();

        if (filteredVideos.isNotEmpty) {
          // 如果当前页面超出了过滤后的视频数量，跳转到最后一个视频
          if (currentPage >= filteredVideos.length) {
            final targetPage = filteredVideos.length - 1;
            _pageController.animateToPage(
              targetPage,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            ).then((_) {
              // 更新当前视频索引并触发播放
              _currentVideoIndex = targetPage;
              _onVideoChanged(targetPage);
            });
          } else {
            // 如果在范围内，跳转到下一个视频
            if (currentPage < filteredVideos.length - 1) {
              _pageController.nextPage(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            }
          }
        }
      }
    });
  }

  void _handleVideoDeleted() {
    // 强制重建以应用过滤（删除的视频会被过滤掉）
    setState(() {});

    // 延迟一帧后检查当前视频状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_pageController.hasClients) {
        final currentPage = _pageController.page?.round() ?? 0;

        // 获取过滤后的视频列表长度
        final store = StoreProvider.of<CluAppState>(context, listen: false);
        final allVideos = store.state.communityFeed.isNotEmpty
            ? store.state.communityFeed
            : _allVideos;
        final blockedIds = CluBlockManager.instance.getBlockedCraftIds();
        final filteredVideos = allVideos.where((video) =>
            !blockedIds.contains(video.craftId)
        ).toList();

        if (filteredVideos.isNotEmpty) {
          // 如果当前页面超出了过滤后的视频数量，跳转到最后一个视频
          if (currentPage >= filteredVideos.length) {
            final targetPage = filteredVideos.length - 1;
            _pageController.animateToPage(
              targetPage,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            ).then((_) {
              // 更新当前视频索引并触发播放
              _currentVideoIndex = targetPage;
              _onVideoChanged(targetPage);
            });
          }
          // 如果在范围内，保持当前位置，不额外切换
        }
      }
    });
  }

  // 删除用户帖子
  Future<void> _deleteUserPost(CluCraftPiece craft) async {
    final confirmed = await showCupertinoDialog<bool>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Delete Post'),
        content: const Text('Are you sure you want to delete this post? This action cannot be undone.'),
        actions: [
          CupertinoDialogAction(
            child: const Text('Cancel'),
            onPressed: () => Navigator.of(context).pop(false),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            child: const Text('Delete'),
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final success = await CluUserPostManager.instance.deletePost(craft.craftId);

        if (success) {
          _showSuccessMessage('Post deleted successfully');
          // 刷新视频列表
          await _loadUserPosts();
          _loadCommunityFeed();
        } else {
          _showErrorMessage('Failed to delete post');
        }
      } catch (e) {
        _showErrorMessage('Failed to delete post');
        debugPrint('Delete post error: $e');
      }
    }
  }

  // 显示成功消息
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: CluLuxuryTheme.oliveDrab,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // 显示错误消息
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // 导航到屏蔽管理页面
  void _navigateToBlockManagement() {
    Navigator.of(context).push(
      CupertinoPageRoute(
        builder: (context) => const CluBlockManagementView(),
      ),
    );
  }

  // 滚动到顶部
  void scrollToTop() {
    if (_pageController.hasClients) {
      _pageController.animateToPage(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      setState(() {
        _currentVideoIndex = 0;
        _shouldAutoPlay = true;
      });
    }
  }

  @override
  bool get wantKeepAlive => true;
}

// ViewModel for Redux connection
class _ViewModel {
  final List<CluCraftPiece> communityFeed;
  final bool isLoading;
  final Function(String) onLikeCraft;

  _ViewModel({
    required this.communityFeed,
    required this.isLoading,
    required this.onLikeCraft,
  });

  static _ViewModel fromStore(Store<CluAppState> store) {
    return _ViewModel(
      communityFeed: store.state.communityFeed,
      isLoading: store.state.isPatchSquareLoading,
      onLikeCraft: (craftId) {
        store.dispatch(CluLikeCraftAction(craftId: craftId));
      },
    );
  }
}
