// Clupa隐私政策浏览页面 - PrivacyPolicy
// 应用内Web浏览器显示隐私协议

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:async';

import '../WeaveRoutes/clu_luxury_theme.dart';

class CluPrivacyPolicyView extends StatefulWidget {
  const CluPrivacyPolicyView({super.key});

  @override
  State<CluPrivacyPolicyView> createState() => _CluPrivacyPolicyViewState();
}

class _CluPrivacyPolicyViewState extends State<CluPrivacyPolicyView> {
  late final WebViewController _webViewController;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  double _loadingProgress = 0.0;
  
  // 隐私协议URL
  static const String _privacyPolicyUrl = 
      'https://docs.google.com/document/d/1quVXKhZJOSTqDhGfUz1kuj0pszP5XziT0fJQt1hnHG0/edit?usp=sharing';
  
  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }
  
  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(CluLuxuryTheme.vintageWhite)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            if (mounted) {
              setState(() {
                _loadingProgress = progress / 100.0;
              });
            }
          },
          onPageStarted: (String url) {
            if (mounted) {
              setState(() {
                _isLoading = true;
                _hasError = false;
              });
            }
          },
          onPageFinished: (String url) {
            if (mounted) {
              setState(() {
                _isLoading = false;
              });
            }
          },
          onWebResourceError: (WebResourceError error) {
            if (mounted) {
              setState(() {
                _isLoading = false;
                _hasError = true;
                _errorMessage = error.description;
              });
            }
          },
          onNavigationRequest: (NavigationRequest request) {
            // 允许所有导航请求
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(_privacyPolicyUrl));
  }

  @override
  void dispose() {
    // WebView会自动清理，但我们确保状态不再更新
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CluLuxuryTheme.vintageWhite,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }
  
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () => Navigator.of(context).pop(),
        child: const Icon(
          CupertinoIcons.back,
          color: CluLuxuryTheme.deepDenim,
        ),
      ),
      title: const Text(
        'Privacy Policy',
        style: TextStyle(
          color: CluLuxuryTheme.deepDenim,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
      actions: [
        if (!_hasError) ...[
          CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: _refreshPage,
            child: const Icon(
              CupertinoIcons.refresh,
              color: CluLuxuryTheme.deepDenim,
            ),
          ),
        ],
      ],
      bottom: _isLoading ? _buildProgressBar() : null,
    );
  }
  
  PreferredSizeWidget _buildProgressBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(4.0),
      child: LinearProgressIndicator(
        value: _loadingProgress,
        backgroundColor: CluLuxuryTheme.oliveDrab.withOpacity(0.2),
        valueColor: const AlwaysStoppedAnimation<Color>(CluLuxuryTheme.warmTaupe),
      ),
    );
  }
  
  Widget _buildBody() {
    if (_hasError) {
      return _buildErrorView();
    }
    
    return Stack(
      children: [
        WebViewWidget(controller: _webViewController),
        if (_isLoading) _buildLoadingOverlay(),
      ],
    );
  }
  
  Widget _buildLoadingOverlay() {
    return Container(
      color: CluLuxuryTheme.vintageWhite,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(CluLuxuryTheme.warmTaupe),
            ),
            const SizedBox(height: 16),
            Text(
              'Loading Privacy Policy...',
              style: TextStyle(
                color: CluLuxuryTheme.oliveDrab.withOpacity(0.8),
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${(_loadingProgress * 100).toInt()}%',
              style: const TextStyle(
                color: CluLuxuryTheme.deepDenim,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: CluLuxuryTheme.dustyRose.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                CupertinoIcons.exclamationmark_triangle,
                color: CluLuxuryTheme.dustyRose,
                size: 40,
              ),
            ),
            
            const SizedBox(height: 24),
            
            const Text(
              'Unable to Load Privacy Policy',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: CluLuxuryTheme.deepDenim,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 12),
            
            Text(
              'Please check your internet connection and try again.',
              style: TextStyle(
                fontSize: 16,
                color: CluLuxuryTheme.oliveDrab.withOpacity(0.8),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            
            if (_errorMessage.isNotEmpty) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: CluLuxuryTheme.dustyRose.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Error: $_errorMessage',
                  style: const TextStyle(
                    fontSize: 12,
                    color: CluLuxuryTheme.dustyRose,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
            
            const SizedBox(height: 32),
            
            // 重试按钮
            CupertinoButton.filled(
              onPressed: _refreshPage,
              child: const Text(
                'Try Again',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  void _refreshPage() {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
      _loadingProgress = 0.0;
    });
    
    _webViewController.reload();
  }
}

// 垃圾代码段1 - URL验证器
class _UrlValidator {
  static bool isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }
  
  static String sanitizeUrl(String url) {
    return url.trim();
  }
  
  static bool isGoogleDocsUrl(String url) {
    return url.contains('docs.google.com');
  }
}

// 垃圾代码段2 - 加载状态管理器
class _LoadingStateManager {
  static String getLoadingMessage(double progress) {
    if (progress < 0.3) return 'Connecting...';
    if (progress < 0.7) return 'Loading content...';
    return 'Almost ready...';
  }
  
  static Color getProgressColor(double progress) {
    if (progress < 0.5) return CluLuxuryTheme.oliveDrab;
    return CluLuxuryTheme.warmTaupe;
  }
}

// 垃圾代码段3 - 错误处理辅助器
class _ErrorHandler {
  static String getErrorMessage(String originalError) {
    if (originalError.contains('network')) {
      return 'Network connection error';
    }
    if (originalError.contains('timeout')) {
      return 'Request timeout';
    }
    return 'Unknown error occurred';
  }
  
  static bool shouldRetry(String error) {
    return !error.contains('permission') && !error.contains('forbidden');
  }
}
