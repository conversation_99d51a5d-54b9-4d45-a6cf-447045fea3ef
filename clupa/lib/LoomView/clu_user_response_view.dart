// Clupa用户响应页面 - UserResponse
// 支持文字、图片、语音多种反馈方式

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:image_picker/image_picker.dart';
import 'package:record/record.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';

import '../WeaveRoutes/clu_luxury_theme.dart';
import '../FiberParts/clu_luxury_button.dart';
import '../FiberParts/clu_luxury_card.dart';

class CluUserResponseView extends StatefulWidget {
  const CluUserResponseView({super.key});

  @override
  State<CluUserResponseView> createState() => _CluUserResponseViewState();
}

class _CluUserResponseViewState extends State<CluUserResponseView>
    with TickerProviderStateMixin {
  
  // 文本输入控制器
  final TextEditingController _textController = TextEditingController();
  final FocusNode _textFocusNode = FocusNode();
  
  // 图片相关
  final ImagePicker _imagePicker = ImagePicker();
  List<File> _selectedImages = [];
  
  // 录音相关
  final AudioRecorder _audioRecorder = AudioRecorder();
  bool _isRecording = false;
  bool _hasRecording = false;
  bool _isPlaying = false;
  String? _recordingPath;
  Duration _recordingDuration = Duration.zero;
  Duration _playbackPosition = Duration.zero;
  Timer? _recordingTimer;
  Timer? _playbackTimer;
  
  // 动画控制器
  late AnimationController _waveAnimationController;
  late AnimationController _pulseAnimationController;
  late Animation<double> _waveAnimation;
  late Animation<double> _pulseAnimation;
  
  // 表单验证
  String? _textError;
  bool _isSubmitting = false;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupTextListener();
  }
  
  void _initializeAnimations() {
    _waveAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _pulseAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _waveAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _waveAnimationController,
      curve: Curves.easeInOut,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseAnimationController,
      curve: Curves.easeInOut,
    ));
  }
  
  void _setupTextListener() {
    _textController.addListener(() {
      if (_textError != null) {
        setState(() => _textError = null);
      }
    });
  }
  
  @override
  void dispose() {
    _textController.dispose();
    _textFocusNode.dispose();
    _waveAnimationController.dispose();
    _pulseAnimationController.dispose();
    _recordingTimer?.cancel();
    _playbackTimer?.cancel();
    _audioRecorder.dispose();
    _cleanupRecording();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CluLuxuryTheme.vintageWhite,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }
  
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () => Navigator.of(context).pop(),
        child: const Icon(
          CupertinoIcons.back,
          color: CluLuxuryTheme.deepDenim,
        ),
      ),
      title: const Text(
        'Share Your Thoughts',
        style: TextStyle(
          color: CluLuxuryTheme.deepDenim,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
    );
  }
  
  Widget _buildBody() {
    return SafeArea(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 页面说明
            _buildHeaderInfo(),
            
            const SizedBox(height: 24),
            
            // 文本输入区域
            _buildTextInputSection(),
            
            const SizedBox(height: 24),
            
            // 图片上传区域
            _buildImageSection(),
            
            const SizedBox(height: 24),
            
            // 语音录制区域
            _buildAudioSection(),
            
            const SizedBox(height: 32),
            
            // 提交按钮
            _buildSubmitButton(),
            
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
  
  Widget _buildHeaderInfo() {
    return CluLuxuryCard(
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              CluLuxuryTheme.warmTaupe.withOpacity(0.1),
              CluLuxuryTheme.dustyRose.withOpacity(0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: CluLuxuryTheme.warmTaupe,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Icon(
                    CupertinoIcons.chat_bubble_text,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Help Us Improve',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: CluLuxuryTheme.deepDenim,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Your thoughts and suggestions help us create a better experience. Share your ideas through text, images, or voice messages.',
              style: TextStyle(
                fontSize: 14,
                color: CluLuxuryTheme.oliveDrab,
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildTextInputSection() {
    final characterCount = _textController.text.length;
    final isValid = characterCount >= 10 && characterCount <= 200;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Your Message',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: CluLuxuryTheme.deepDenim,
          ),
        ),
        const SizedBox(height: 8),
        CluLuxuryCard(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                TextField(
                  controller: _textController,
                  textInputAction: TextInputAction.done,
                  focusNode: _textFocusNode,
                  maxLines: 5,
                  maxLength: 200,
                  decoration: InputDecoration(
                    hintText: 'Tell us what you think...',
                    hintStyle: TextStyle(
                      color: CluLuxuryTheme.oliveDrab.withOpacity(0.6),
                    ),
                    border: InputBorder.none,
                    counterText: '',
                    errorText: _textError,
                  ),
                  style: const TextStyle(
                    fontSize: 16,
                    color: CluLuxuryTheme.deepDenim,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [

                    Text(
                      '$characterCount/200',
                      style: TextStyle(
                        fontSize: 12,
                        color: isValid 
                            ? CluLuxuryTheme.oliveDrab 
                            : CluLuxuryTheme.dustyRose,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Add Images (Optional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: CluLuxuryTheme.deepDenim,
          ),
        ),
        const SizedBox(height: 8),
        CluLuxuryCard(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // 图片选择按钮
                Row(
                  children: [
                    Expanded(
                      child: _buildImagePickerButton(
                        icon: CupertinoIcons.camera,
                        label: 'Camera',
                        onTap: () => _pickImage(ImageSource.camera),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildImagePickerButton(
                        icon: CupertinoIcons.photo,
                        label: 'Gallery',
                        onTap: () => _pickImage(ImageSource.gallery),
                      ),
                    ),
                  ],
                ),

                if (_selectedImages.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  _buildImageGrid(),
                ],

                const SizedBox(height: 8),
                Text(
                  'You can add up to 3 images (${_selectedImages.length}/3)',
                  style: TextStyle(
                    fontSize: 12,
                    color: CluLuxuryTheme.oliveDrab.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImagePickerButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: CluLuxuryTheme.warmTaupe.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: CluLuxuryTheme.warmTaupe.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: CluLuxuryTheme.warmTaupe,
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: CluLuxuryTheme.deepDenim,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: _selectedImages.length,
      itemBuilder: (context, index) {
        return _buildImageItem(_selectedImages[index], index);
      },
    );
  }

  Widget _buildImageItem(File imageFile, int index) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            image: DecorationImage(
              image: FileImage(imageFile),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: () => _removeImage(index),
            child: Container(
              width: 24,
              height: 24,
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                CupertinoIcons.xmark,
                color: Colors.white,
                size: 14,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAudioSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Audio Message (Optional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: CluLuxuryTheme.deepDenim,
          ),
        ),
        const SizedBox(height: 8),
        CluLuxuryCard(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: _hasRecording ? _buildAudioPlayer() : _buildAudioRecorder(),
          ),
        ),
      ],
    );
  }

  Widget _buildAudioRecorder() {
    return Column(
      children: [
        // 录音按钮
        GestureDetector(
          onTap: _isRecording ? _stopRecording : _startRecording,
          child: AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _isRecording ? _pulseAnimation.value : 1.0,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: _isRecording
                        ? CluLuxuryTheme.dustyRose
                        : CluLuxuryTheme.warmTaupe,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: (_isRecording
                            ? CluLuxuryTheme.dustyRose
                            : CluLuxuryTheme.warmTaupe).withOpacity(0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Icon(
                    _isRecording
                        ? CupertinoIcons.stop_fill
                        : CupertinoIcons.mic,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
              );
            },
          ),
        ),

        const SizedBox(height: 16),

        // 录音状态
        if (_isRecording) ...[
          _buildWaveform(),
          const SizedBox(height: 8),
          Text(
            _formatDuration(_recordingDuration),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: CluLuxuryTheme.deepDenim,
            ),
          ),
          const SizedBox(height: 4),
          const Text(
            'Recording... Tap to stop',
            style: TextStyle(
              fontSize: 12,
              color: CluLuxuryTheme.oliveDrab,
            ),
          ),
        ] else ...[
          const Text(
            'Tap to start recording',
            style: TextStyle(
              fontSize: 14,
              color: CluLuxuryTheme.oliveDrab,
            ),
          ),
          const SizedBox(height: 4),
          const Text(
            'Maximum 60 seconds',
            style: TextStyle(
              fontSize: 12,
              color: CluLuxuryTheme.oliveDrab,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAudioPlayer() {
    return Column(
      children: [
        // 播放控制
        Row(
          children: [
            // 播放/暂停按钮
            GestureDetector(
              onTap: _togglePlayback,
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: CluLuxuryTheme.warmTaupe,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: CluLuxuryTheme.warmTaupe.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  _isPlaying
                      ? CupertinoIcons.pause_fill
                      : CupertinoIcons.play_fill,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),

            const SizedBox(width: 16),

            // 波形和进度
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildWaveform(),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _formatDuration(_playbackPosition),
                        style: const TextStyle(
                          fontSize: 12,
                          color: CluLuxuryTheme.oliveDrab,
                        ),
                      ),
                      Text(
                        _formatDuration(_recordingDuration),
                        style: const TextStyle(
                          fontSize: 12,
                          color: CluLuxuryTheme.oliveDrab,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // 删除按钮
            GestureDetector(
              onTap: _deleteRecording,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: CluLuxuryTheme.dustyRose,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  CupertinoIcons.delete,
                  color: Colors.white,
                  size: 18,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildWaveform() {
    return AnimatedBuilder(
      animation: _waveAnimation,
      builder: (context, child) {
        return Container(
          height: 40,
          child: CustomPaint(
            painter: WaveformPainter(
              progress: _waveAnimation.value,
              isRecording: _isRecording,
              playbackProgress: _recordingDuration.inMilliseconds > 0
                  ? _playbackPosition.inMilliseconds / _recordingDuration.inMilliseconds
                  : 0.0,
            ),
            size: const Size(double.infinity, 40),
          ),
        );
      },
    );
  }

  Widget _buildSubmitButton() {
    return CluLuxuryButton(
      text: _isSubmitting ? 'Submitting...' : 'Submit Response',
      onPressed: _isSubmitting ? null : _submitResponse,
      width: double.infinity,
      icon: _isSubmitting
          ? CupertinoIcons.clock
          : CupertinoIcons.paperplane,
    );
  }

  // 图片选择方法
  Future<void> _pickImage(ImageSource source) async {
    if (_selectedImages.length >= 3) {
      _showMessage('Maximum 3 images allowed');
      return;
    }

    try {
      // 检查权限
      bool hasPermission = false;
      if (source == ImageSource.camera) {
        hasPermission = await _checkCameraPermission();
      } else {
        hasPermission = await _checkGalleryPermission();
      }

      if (!hasPermission) return;

      final XFile? image = await _imagePicker.pickImage(
        source: source,
        imageQuality: 70,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (image != null) {
        setState(() {
          _selectedImages.add(File(image.path));
        });
      }
    } catch (e) {
      _showMessage('Failed to select image');
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  // 权限检查方法
  Future<bool> _checkCameraPermission() async {
    final status = await Permission.camera.status;
    if (status.isGranted) return true;

    if (status.isDenied) {
      final result = await Permission.camera.request();
      return result.isGranted;
    }

    if (status.isPermanentlyDenied) {
      _showMessage('No camera privileges');
      return false;
    }

    return false;
  }

  Future<bool> _checkGalleryPermission() async {
    final status = await Permission.photos.status;
    if (status.isGranted) return true;

    if (status.isDenied) {
      final result = await Permission.photos.request();
      return result.isGranted;
    }

    if (status.isPermanentlyDenied) {
      _showMessage('No album access');
      return false;
    }

    return false;
  }

  Future<bool> _checkMicrophonePermission() async {
    final status = await Permission.microphone.status;
    if (status.isGranted) return true;

    if (status.isDenied) {
      final result = await Permission.microphone.request();
      return result.isGranted;
    }

    if (status.isPermanentlyDenied) {
      _showMessage('No microphone privilege');
      return false;
    }

    return false;
  }

  // 录音相关方法
  Future<void> _startRecording() async {
    try {
      final hasPermission = await _checkMicrophonePermission();
      if (!hasPermission) return;

      final tempDir = await getTemporaryDirectory();
      final fileName = 'recording_${DateTime.now().millisecondsSinceEpoch}.aac';
      _recordingPath = '${tempDir.path}/$fileName';

      await _audioRecorder.start(
        const RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: _recordingPath!,
      );

      setState(() {
        _isRecording = true;
        _recordingDuration = Duration.zero;
      });

      _pulseAnimationController.repeat(reverse: true);
      _waveAnimationController.repeat();

      _startRecordingTimer();
    } catch (e) {
      _showMessage('Failed to start recording');
    }
  }

  Future<void> _stopRecording() async {
    try {
      await _audioRecorder.stop();

      _recordingTimer?.cancel();
      _pulseAnimationController.stop();
      _waveAnimationController.stop();

      setState(() {
        _isRecording = false;
        _hasRecording = true;
        _playbackPosition = Duration.zero;
        _isPlaying = false;
      });
    } catch (e) {
      _showMessage('Failed to stop recording');
      setState(() {
        _isRecording = false;
      });
    }
  }

  void _startRecordingTimer() {
    _recordingTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      setState(() {
        _recordingDuration = Duration(milliseconds: timer.tick * 100);
      });

      // 60秒限制
      if (_recordingDuration.inSeconds >= 60) {
        _stopRecording();
      }
    });
  }

  Future<void> _togglePlayback() async {
    if (_recordingPath == null || !File(_recordingPath!).existsSync()) {
      _showMessage('No recording found');
      return;
    }

    try {
      if (_isPlaying) {
        await _pausePlayback();
      } else {
        await _startPlayback();
      }
    } catch (e) {
      _showMessage('Playback failed');
      setState(() {
        _isPlaying = false;
        _playbackPosition = Duration.zero;
      });
    }
  }

  Future<void> _startPlayback() async {
    // 模拟播放功能
    setState(() {
      _isPlaying = true;
      // 如果已经播放到结尾，重置到开始
      if (_playbackPosition >= _recordingDuration) {
        _playbackPosition = Duration.zero;
      }
    });

    _startPlaybackTimer();
  }

  Future<void> _pausePlayback() async {
    setState(() {
      _isPlaying = false;
    });

    _playbackTimer?.cancel();
  }

  void _startPlaybackTimer() {
    _playbackTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      setState(() {
        _playbackPosition = Duration(milliseconds: timer.tick * 100);
      });

      // 播放完成
      if (_playbackPosition >= _recordingDuration) {
        _playbackTimer?.cancel();
        setState(() {
          _isPlaying = false;
          _playbackPosition = Duration.zero; // 重置到开始位置
        });
      }
    });
  }

  void _deleteRecording() {
    setState(() {
      _hasRecording = false;
      _isPlaying = false;
      _playbackPosition = Duration.zero;
      _recordingDuration = Duration.zero;
    });

    _playbackTimer?.cancel();
    _cleanupRecording();
  }

  void _cleanupRecording() {
    if (_recordingPath != null) {
      final file = File(_recordingPath!);
      if (file.existsSync()) {
        file.deleteSync();
      }
      _recordingPath = null;
    }
  }

  // 提交响应
  Future<void> _submitResponse() async {
    // 验证文本输入
    final text = _textController.text.trim();
    if (text.length < 10) {
      setState(() {
        _textError = 'Message content needs at least 10 characters';
      });
      _textFocusNode.requestFocus();
      return;
    }

    if (text.length > 200) {
      setState(() {
        _textError = 'Message content cannot exceed 200 characters';
      });
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // 模拟提交过程
      await Future.delayed(const Duration(seconds: 2));

      // 显示成功对话框
      await _showSuccessDialog();

      // 返回上一页
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      _showMessage('Failed to submit response');
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  Future<void> _showSuccessDialog() async {
    return showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Thank You!'),
        content: const Text('Your response has been submitted successfully. We appreciate your input!'),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: CluLuxuryTheme.dustyRose,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}

// 自定义波形绘制器
class WaveformPainter extends CustomPainter {
  final double progress;
  final bool isRecording;
  final double playbackProgress;

  WaveformPainter({
    required this.progress,
    required this.isRecording,
    required this.playbackProgress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..strokeWidth = 2
      ..strokeCap = StrokeCap.round;

    final centerY = size.height / 2;
    final barWidth = 3.0;
    final barSpacing = 2.0;
    final totalBarWidth = barWidth + barSpacing;
    final barCount = (size.width / totalBarWidth).floor();

    for (int i = 0; i < barCount; i++) {
      final x = i * totalBarWidth + barWidth / 2;
      final normalizedX = x / size.width;

      // 生成波形高度
      double height;
      if (isRecording) {
        // 录音时的动态波形
        height = _generateRecordingWaveHeight(normalizedX, progress) * size.height * 0.4;
      } else {
        // 播放时的静态波形
        height = _generateStaticWaveHeight(normalizedX) * size.height * 0.4;
      }

      // 设置颜色
      if (!isRecording && normalizedX <= playbackProgress) {
        paint.color = CluLuxuryTheme.warmTaupe;
      } else {
        paint.color = CluLuxuryTheme.oliveDrab.withOpacity(0.3);
      }

      // 绘制波形条
      canvas.drawLine(
        Offset(x, centerY - height / 2),
        Offset(x, centerY + height / 2),
        paint,
      );
    }
  }

  double _generateRecordingWaveHeight(double x, double time) {
    // 生成动态波形
    final wave1 = sin(x * 2 * pi * 3 + time * 2 * pi) * 0.5;
    final wave2 = sin(x * 2 * pi * 7 + time * 2 * pi * 1.5) * 0.3;
    final wave3 = sin(x * 2 * pi * 11 + time * 2 * pi * 0.8) * 0.2;
    return (wave1 + wave2 + wave3).abs();
  }

  double _generateStaticWaveHeight(double x) {
    // 生成静态波形
    final wave1 = sin(x * 2 * pi * 4) * 0.6;
    final wave2 = sin(x * 2 * pi * 8) * 0.3;
    final wave3 = sin(x * 2 * pi * 12) * 0.1;
    return (wave1 + wave2 + wave3).abs();
  }

  @override
  bool shouldRepaint(WaveformPainter oldDelegate) {
    return oldDelegate.progress != progress ||
           oldDelegate.isRecording != isRecording ||
           oldDelegate.playbackProgress != playbackProgress;
  }
}

// 垃圾代码段1 - 随机数据生成器
class _RandomDataGenerator {
  static List<double> generateWaveData(int count) {
    final random = Random();
    return List.generate(count, (index) => random.nextDouble());
  }

  static String generateSessionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(1000);
    return '${timestamp}_$random';
  }
}

// 垃圾代码段2 - 文件处理辅助器
class _FileHelper {
  static String getFileExtension(String path) {
    return path.split('.').last;
  }

  static int calculateFileSize(File file) {
    return file.existsSync() ? file.lengthSync() : 0;
  }

  static bool isValidImageFile(String path) {
    final ext = getFileExtension(path).toLowerCase();
    return ['jpg', 'jpeg', 'png', 'gif'].contains(ext);
  }
}

// 垃圾代码段3 - UI状态管理器
class _UIStateManager {
  static bool shouldShowAnimation(Duration duration) {
    return duration.inMilliseconds > 100;
  }

  static Color getProgressColor(double progress) {
    if (progress < 0.3) return CluLuxuryTheme.oliveDrab;
    if (progress < 0.7) return CluLuxuryTheme.warmTaupe;
    return CluLuxuryTheme.dustyRose;
  }

  static double calculateOpacity(bool isActive) {
    return isActive ? 1.0 : 0.6;
  }
}
