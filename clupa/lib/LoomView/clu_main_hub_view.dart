// Clupa主工作台页面 - MainHub
// 竖屏视频流形式，类似TikTok的沉浸式体验

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

import '../ThreadStore/clu_app_state.dart';
import '../StitchMaster/clu_needle_actions.dart';
import '../ThreadStore/clu_agreement_manager.dart';
import 'clu_agreement_dialog.dart';
import 'clu_splash_screen.dart';
import '../WeaveRoutes/clu_luxury_theme.dart';
import '../FiberParts/clu_luxury_nav_bar.dart';
import 'clu_video_feed_view.dart';
import 'clu_create_craft_view.dart';
import 'clu_ai_sage_view.dart';
import 'clu_vip_profile_view.dart';

class CluMainHubView extends StatefulWidget {
  const CluMainHubView({super.key});

  @override
  State<CluMainHubView> createState() => _CluMainHubViewState();
}

class _CluMainHubViewState extends State<CluMainHubView> with WidgetsBindingObserver {
  late PageController _pageController;
  bool _agreementAccepted = false;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _agreementAccepted = CluAgreementManager.instance.hasAcceptedAgreement();
    _checkAgreementStatus();
    WidgetsBinding.instance.addObserver(this);
  }

  // 检查协议同意状态
  void _checkAgreementStatus() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !CluAgreementManager.instance.hasAcceptedAgreement()) {
        _showAgreementDialog();
      }
    });
  }

  // 显示协议弹窗
  void _showAgreementDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => CluAgreementDialog(
        onAccepted: () {
          Navigator.of(context).pop();
          // 协议同意后更新状态并允许视频播放
          setState(() {
            _agreementAccepted = true;
          });
        },
        onRejected: () {
          Navigator.of(context).pop();
          _handleAgreementRejected();
        },
      ),
    );
  }

  // 处理协议拒绝
  void _handleAgreementRejected() {
    // 返回到启动页
    Navigator.of(context).pushReplacement(
      CupertinoPageRoute(
        builder: (context) => const CluSplashScreen(),
      ),
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _pageController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // 当应用进入后台或非活跃状态时，暂停视频播放
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.inactive ||
        state == AppLifecycleState.detached) {
      _pauseVideoPlayback();
    }
  }

  // 暂停视频播放的方法
  void _pauseVideoPlayback() {
    // 通过Redux发送暂停视频的Action
    final store = StoreProvider.of<CluAppState>(context, listen: false);
    store.dispatch(CluPauseVideoAction());
  }

  void _onTabTapped(int index) {
    // 触觉反馈
    HapticFeedback.lightImpact();

    debugPrint('Tab tapped: $index');

    // 只更新Redux状态，页面切换由onWillChange处理
    StoreProvider.of<CluAppState>(context).dispatch(
      CluChangeTabAction(tabIndex: index),
    );
  }

  @override
  Widget build(BuildContext context) {
    return StoreConnector<CluAppState, _ViewModel>(
      converter: _ViewModel.fromStore,
      onWillChange: (oldViewModel, newViewModel) {
        // 当tab索引变化时，切换PageView
        if (oldViewModel != null &&
            newViewModel != null &&
            oldViewModel.selectedTabIndex != newViewModel.selectedTabIndex) {
          debugPrint('Redux state changed: ${oldViewModel.selectedTabIndex} -> ${newViewModel.selectedTabIndex}');
          // 使用jumpToPage确保直接跳转，避免动画被中断
          _pageController.jumpToPage(newViewModel.selectedTabIndex);
        }
      },
      builder: (context, viewModel) {
        return Scaffold(
          backgroundColor: Colors.black, // 视频流背景为黑色

          // 主要内容区域
          body: PageView(
            controller: _pageController,
            onPageChanged: (index) {
              // 只在当前Redux状态与页面索引不一致时才更新
              debugPrint('PageView changed to: $index, current Redux state: ${viewModel.selectedTabIndex}');
              if (viewModel.selectedTabIndex != index) {
                debugPrint('Updating Redux state from PageView');
                viewModel.onTabChanged(index);
              }
            },
            children: [
              CluVideoFeedView(canAutoPlay: _agreementAccepted),      // 视频流页面
              const CluCreateCraftView(),    // 创作页面
              const CluAiSageView(),         // AI助手页面
              const CluVipProfileView(),     // 个人档案页面
            ],
          ),

          // 底部导航栏
          bottomNavigationBar: CluLuxuryNavBar(
            currentIndex: viewModel.selectedTabIndex,
            onTap: _onTabTapped,
            items: CluNavBarItems.mainTabs,
          ),
        );
      },
    );
  }
}

// ViewModel for Redux connection
class _ViewModel {
  final int selectedTabIndex;
  final bool isLoading;
  final Function(int) onTabChanged;

  _ViewModel({
    required this.selectedTabIndex,
    required this.isLoading,
    required this.onTabChanged,
  });

  static _ViewModel fromStore(Store<CluAppState> store) {
    return _ViewModel(
      selectedTabIndex: store.state.selectedTabIndex,
      isLoading: store.state.isAppLoading,
      onTabChanged: (index) {
        store.dispatch(CluChangeTabAction(tabIndex: index));
      },
    );
  }
}
