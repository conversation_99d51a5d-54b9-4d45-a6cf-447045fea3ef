// Clupa启动页面
// 应用启动时的欢迎界面，包含Logo动画和加载状态

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'dart:async';

import '../WeaveRoutes/clu_luxury_theme.dart';
import '../FiberParts/clu_luxury_button.dart';
import 'clu_main_hub_view.dart';

class CluSplashScreen extends StatefulWidget {
  const CluSplashScreen({super.key});

  @override
  State<CluSplashScreen> createState() => _CluSplashScreenState();
}

class _CluSplashScreenState extends State<CluSplashScreen>
    with TickerProviderStateMixin {
  
  late AnimationController _logoController;
  late AnimationController _textController;
  late AnimationController _progressController;
  
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoOpacityAnimation;
  late Animation<double> _textOpacityAnimation;
  late Animation<Offset> _textSlideAnimation;
  late Animation<double> _progressAnimation;

  bool _showStartButton = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startSplashSequence();
  }

  void _initializeAnimations() {
    // Logo动画控制器
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // 文字动画控制器
    _textController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    // 进度条动画控制器
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // Logo缩放动画
    _logoScaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    // Logo透明度动画
    _logoOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
    ));

    // 文字透明度动画
    _textOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeIn,
    ));

    // 文字滑动动画
    _textSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeOutCubic,
    ));

    // 进度条动画
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));
  }

  void _startSplashSequence() async {
    // 触觉反馈
    HapticFeedback.lightImpact();

    // 启动Logo动画
    _logoController.forward();

    // 延迟启动文字动画
    await Future.delayed(const Duration(milliseconds: 800));
    if (mounted) {
      _textController.forward();
    }

    // 延迟启动进度条动画
    await Future.delayed(const Duration(milliseconds: 500));
    if (mounted) {
      _progressController.forward();
    }

    // 等待所有动画完成后显示启动按钮
    await Future.delayed(const Duration(milliseconds: 2500));
    if (mounted) {
      setState(() {
        _showStartButton = true;
      });
    }
  }

  void _navigateToMainHub() {
    HapticFeedback.mediumImpact();
    Navigator.of(context).pushReplacement(
      CupertinoPageRoute(
        builder: (context) => const CluMainHubView(),
      ),
    );
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CluLuxuryTheme.deepDenim,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              CluLuxuryTheme.deepDenim,
              CluLuxuryTheme.deepDenim.withOpacity(0.8),
              CluLuxuryTheme.warmTaupe.withOpacity(0.3),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 顶部空间
              const Spacer(flex: 2),

              // Logo区域
              AnimatedBuilder(
                animation: _logoController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _logoScaleAnimation.value,
                    child: Opacity(
                      opacity: _logoOpacityAnimation.value,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: CluLuxuryTheme.warmTaupe,
                          borderRadius: BorderRadius.circular(30),
                          boxShadow: [
                            BoxShadow(
                              color: CluLuxuryTheme.warmTaupe.withOpacity(0.3),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: const Icon(
                          CupertinoIcons.scissors,
                          size: 60,
                          color: CluLuxuryTheme.vintageWhite,
                        ),
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: 30),

              // 应用名称和标语
              AnimatedBuilder(
                animation: _textController,
                builder: (context, child) {
                  return SlideTransition(
                    position: _textSlideAnimation,
                    child: Opacity(
                      opacity: _textOpacityAnimation.value,
                      child: Column(
                        children: [
                          // 应用名称
                          const Text(
                            'Clupa',
                            style: TextStyle(
                              fontSize: 48,
                              fontWeight: FontWeight.w700,
                              color: CluLuxuryTheme.vintageWhite,
                              letterSpacing: 2,
                            ),
                          ),
                          
                          const SizedBox(height: 12),
                          
                          // 标语
                          Text(
                            'Craft • Share • Inspire',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w400,
                              color: CluLuxuryTheme.vintageWhite.withOpacity(0.8),
                              letterSpacing: 1.5,
                            ),
                          ),
                          
                          const SizedBox(height: 8),
                          
                          // 副标题
                          Text(
                            'Your AI-Powered Patchwork Community',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w300,
                              color: CluLuxuryTheme.vintageWhite.withOpacity(0.6),
                              letterSpacing: 0.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),

              const Spacer(flex: 2),

              // 启动按钮区域
              AnimatedOpacity(
                opacity: _showStartButton ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 800),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 800),
                  curve: Curves.easeOutBack,
                  transform: Matrix4.translationValues(
                    0,
                    _showStartButton ? 0 : 50,
                    0
                  ),
                  child: Column(
                    children: [
                      // 欢迎文字
                      Text(
                        'Welcome to your creative journey',
                        style: TextStyle(
                          fontSize: 16,
                          color: CluLuxuryTheme.vintageWhite.withOpacity(0.8),
                          fontWeight: FontWeight.w400,
                          letterSpacing: 0.5,
                        ),
                      ),

                      const SizedBox(height: 30),

                      // 启动按钮
                      Container(
                        width: 200,
                        child: CluLuxuryButton(
                          text: 'Get Started',
                          onPressed: _navigateToMainHub,
                          type: CluButtonType.primary,
                          size: CluButtonSize.large,
                        ),
                      ),

                      const SizedBox(height: 20),

                      // 提示文字
                      Text(
                        'Tap to enter Clupa',
                        style: TextStyle(
                          fontSize: 12,
                          color: CluLuxuryTheme.vintageWhite.withOpacity(0.5),
                          fontWeight: FontWeight.w300,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 60),
            ],
          ),
        ),
      ),
    );
  }
}
