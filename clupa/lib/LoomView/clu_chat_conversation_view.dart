// Clupa聊天对话页面 - ChatConversation
// 与内容创作者的私聊界面

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'dart:async';

import '../WeaveRoutes/clu_luxury_theme.dart';
import '../FiberParts/clu_luxury_card.dart';
import '../ClothVault/clu_chat_message_model.dart';
import '../ThreadStore/clu_chat_storage_manager.dart';

class CluChatConversationView extends StatefulWidget {
  final String participantId;
  final String participantName;
  final String participantAvatar;

  const CluChatConversationView({
    super.key,
    required this.participantId,
    required this.participantName,
    required this.participantAvatar,
  });

  @override
  State<CluChatConversationView> createState() => _CluChatConversationViewState();
}

class _CluChatConversationViewState extends State<CluChatConversationView>
    with TickerProviderStateMixin {
  
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _messageFocusNode = FocusNode();
  
  late AnimationController _sendButtonController;
  late Animation<double> _sendButtonAnimation;
  
  CluChatSession? _currentSession;
  List<CluChatMessage> _messages = [];
  bool _isLoading = true;
  bool _isSending = false;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeChat();
    _messageController.addListener(_onMessageChanged);
  }
  
  void _initializeAnimations() {
    _sendButtonController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _sendButtonAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _sendButtonController,
      curve: Curves.elasticOut,
    ));
  }
  
  Future<void> _initializeChat() async {
    setState(() => _isLoading = true);
    
    try {
      await CluChatStorageManager.instance.initialize();
      
      _currentSession = await CluChatStorageManager.instance.getOrCreateSession(
        participantId: widget.participantId,
        participantName: widget.participantName,
        participantAvatar: widget.participantAvatar,
      );
      
      _messages = _currentSession?.messages ?? [];
      
      // 滚动到底部
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    } catch (e) {
      debugPrint('Failed to initialize chat: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }
  
  void _onMessageChanged() {
    final hasText = _messageController.text.trim().isNotEmpty;
    if (hasText) {
      _sendButtonController.forward();
    } else {
      _sendButtonController.reverse();
    }
  }
  
  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _messageFocusNode.dispose();
    _sendButtonController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CluLuxuryTheme.vintageWhite,
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingView() : _buildChatView(),
    );
  }
  
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () => Navigator.of(context).pop(),
        child: const Icon(
          CupertinoIcons.back,
          color: CluLuxuryTheme.deepDenim,
        ),
      ),
      title: Row(
        children: [
          // 头像
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: CluLuxuryTheme.warmTaupe.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: ClipOval(
              child: widget.participantAvatar.isNotEmpty
                  ? Image.asset(
                      widget.participantAvatar,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: CluLuxuryTheme.warmTaupe.withOpacity(0.2),
                          child: const Icon(
                            CupertinoIcons.person_fill,
                            color: CluLuxuryTheme.oliveDrab,
                            size: 20,
                          ),
                        );
                      },
                    )
                  : Container(
                      color: CluLuxuryTheme.warmTaupe.withOpacity(0.2),
                      child: const Icon(
                        CupertinoIcons.person_fill,
                        color: CluLuxuryTheme.oliveDrab,
                        size: 20,
                      ),
                    ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // 名字
          Expanded(
            child: Text(
              widget.participantName,
              style: const TextStyle(
                color: CluLuxuryTheme.deepDenim,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
      actions: [
        CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: _showMoreOptions,
          child: const Icon(
            CupertinoIcons.ellipsis,
            color: CluLuxuryTheme.deepDenim,
          ),
        ),
      ],
    );
  }
  
  Widget _buildLoadingView() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(CluLuxuryTheme.warmTaupe),
      ),
    );
  }
  
  Widget _buildChatView() {
    return Column(
      children: [
        // 消息列表
        Expanded(
          child: _messages.isEmpty ? _buildEmptyView() : _buildMessagesList(),
        ),
        
        // 输入框
        _buildMessageInput(),
      ],
    );
  }
  
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: CluLuxuryTheme.warmTaupe.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              CupertinoIcons.chat_bubble_2,
              color: CluLuxuryTheme.warmTaupe,
              size: 40,
            ),
          ),
          
          const SizedBox(height: 16),
          
          Text(
            'Start a conversation with ${widget.participantName}',
            style: const TextStyle(
              fontSize: 16,
              color: CluLuxuryTheme.oliveDrab,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 8),
          
          const Text(
            'Send a message to begin chatting',
            style: TextStyle(
              fontSize: 14,
              color: CluLuxuryTheme.oliveDrab,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  Widget _buildMessagesList() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _messages.length,
      itemBuilder: (context, index) {
        final message = _messages[index];
        return _buildMessageBubble(message);
      },
    );
  }
  
  Widget _buildMessageBubble(CluChatMessage message) {
    final isFromMe = message.isFromMe;
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: isFromMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isFromMe) ...[
            // 对方头像
            Container(
              width: 32,
              height: 32,
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: CluLuxuryTheme.warmTaupe.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: ClipOval(
                child: message.senderAvatar.isNotEmpty
                    ? Image.asset(
                        message.senderAvatar,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: CluLuxuryTheme.warmTaupe.withOpacity(0.2),
                            child: const Icon(
                              CupertinoIcons.person_fill,
                              color: CluLuxuryTheme.oliveDrab,
                              size: 16,
                            ),
                          );
                        },
                      )
                    : Container(
                        color: CluLuxuryTheme.warmTaupe.withOpacity(0.2),
                        child: const Icon(
                          CupertinoIcons.person_fill,
                          color: CluLuxuryTheme.oliveDrab,
                          size: 16,
                        ),
                      ),
              ),
            ),
          ],
          
          // 消息气泡
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isFromMe 
                    ? CluLuxuryTheme.warmTaupe 
                    : CluLuxuryTheme.oliveDrab.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20).copyWith(
                  bottomLeft: isFromMe ? const Radius.circular(20) : const Radius.circular(4),
                  bottomRight: isFromMe ? const Radius.circular(4) : const Radius.circular(20),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.content,
                    style: TextStyle(
                      color: isFromMe ? Colors.white : CluLuxuryTheme.deepDenim,
                      fontSize: 16,
                    ),
                  ),
                  
                  const SizedBox(height: 4),
                  
                  Text(
                    _formatMessageTime(message.timestamp),
                    style: TextStyle(
                      color: isFromMe 
                          ? Colors.white.withOpacity(0.7) 
                          : CluLuxuryTheme.oliveDrab.withOpacity(0.7),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),

          if (isFromMe) ...[
            const SizedBox(width: 8),
            // 当前用户头像
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: CluLuxuryTheme.warmTaupe.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: ClipOval(
                child: message.senderAvatar.isNotEmpty
                    ? Image.asset(
                        message.senderAvatar,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: CluLuxuryTheme.warmTaupe.withOpacity(0.2),
                            child: const Icon(
                              CupertinoIcons.person_fill,
                              color: CluLuxuryTheme.oliveDrab,
                              size: 16,
                            ),
                          );
                        },
                      )
                    : Container(
                        color: CluLuxuryTheme.warmTaupe.withOpacity(0.2),
                        child: const Icon(
                          CupertinoIcons.person_fill,
                          color: CluLuxuryTheme.oliveDrab,
                          size: 16,
                        ),
                      ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: CluLuxuryTheme.oliveDrab.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 输入框
            Expanded(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: CluLuxuryTheme.oliveDrab.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(
                    color: CluLuxuryTheme.oliveDrab.withOpacity(0.1),
                    width: 1,
                  ),
                ),
                child: TextField(
                  controller: _messageController,
                  focusNode: _messageFocusNode,
                  maxLines: null,
                  maxLength: 1000,
                  decoration: const InputDecoration(
                    hintText: 'Type a message...',
                    hintStyle: TextStyle(
                      color: CluLuxuryTheme.oliveDrab,
                      fontSize: 16,
                    ),
                    border: InputBorder.none,
                    counterText: '',
                  ),
                  style: const TextStyle(
                    color: CluLuxuryTheme.deepDenim,
                    fontSize: 16,
                  ),
                  onSubmitted: (_) => _sendMessage(),
                ),
              ),
            ),

            const SizedBox(width: 12),

            // 发送按钮
            AnimatedBuilder(
              animation: _sendButtonAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _sendButtonAnimation.value,
                  child: CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: _isSending ? null : _sendMessage,
                    child: Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        color: _messageController.text.trim().isNotEmpty
                            ? CluLuxuryTheme.warmTaupe
                            : CluLuxuryTheme.oliveDrab.withOpacity(0.3),
                        shape: BoxShape.circle,
                      ),
                      child: _isSending
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Icon(
                              CupertinoIcons.arrow_up,
                              color: Colors.white,
                              size: 20,
                            ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _sendMessage() async {
    final content = _messageController.text.trim();
    if (content.isEmpty || _isSending || _currentSession == null) return;

    setState(() => _isSending = true);

    try {
      // 清空输入框
      _messageController.clear();
      _sendButtonController.reverse();

      // 发送消息
      final message = await CluChatStorageManager.instance.sendMessage(
        sessionId: _currentSession!.sessionId,
        content: content,
        senderId: 'current_user', // 当前用户ID
        senderName: 'You',
        senderAvatar: 'assets/avatars/clu_avatar_01.jpg', // 当前用户头像
      );

      setState(() {
        _messages.add(message);
      });

      // 滚动到底部
      _scrollToBottom();

      // 触觉反馈
      HapticFeedback.lightImpact();

    } catch (e) {
      debugPrint('Failed to send message: $e');
      _showErrorMessage('Failed to send message');
    } finally {
      setState(() => _isSending = false);
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _showMoreOptions() {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: Text('Chat with ${widget.participantName}'),
        actions: [
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
              _clearChatHistory();
            },
            isDestructiveAction: true,
            child: const Text('Clear Chat History'),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ),
    );
  }

  Future<void> _clearChatHistory() async {
    final confirmed = await showCupertinoDialog<bool>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Clear Chat History'),
        content: const Text('Are you sure you want to clear all messages? This action cannot be undone.'),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          CupertinoDialogAction(
            onPressed: () => Navigator.of(context).pop(true),
            isDestructiveAction: true,
            child: const Text('Clear'),
          ),
        ],
      ),
    );

    if (confirmed == true && _currentSession != null) {
      await CluChatStorageManager.instance.clearSessionHistory(_currentSession!.sessionId);
      setState(() {
        _messages.clear();
      });

      _showSuccessMessage('Chat history cleared');
    }
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: CluLuxuryTheme.dustyRose,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: CluLuxuryTheme.warmTaupe,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  String _formatMessageTime(DateTime time) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDate = DateTime(time.year, time.month, time.day);

    if (messageDate == today) {
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    } else {
      final diff = today.difference(messageDate).inDays;
      if (diff == 1) {
        return 'Yesterday';
      } else if (diff < 7) {
        return '${diff}d ago';
      } else {
        return '${time.day}/${time.month}';
      }
    }
  }
}
