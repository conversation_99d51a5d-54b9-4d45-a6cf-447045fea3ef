// Clupa屏蔽管理页面 - BlockManagement
// 管理用户屏蔽和举报的内容

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:io';

import '../WeaveRoutes/clu_luxury_theme.dart';
import '../FiberParts/clu_luxury_card.dart';
import '../FiberParts/clu_luxury_button.dart';
import '../ThreadStore/clu_block_manager.dart';
import '../ThreadStore/clu_thumbnail_manager.dart';

class CluBlockManagementView extends StatefulWidget {
  const CluBlockManagementView({super.key});

  @override
  State<CluBlockManagementView> createState() => _CluBlockManagementViewState();
}

class _CluBlockManagementViewState extends State<CluBlockManagementView>
    with TickerProviderStateMixin {
  
  late TabController _tabController;
  List<CluBlockedItem> _allBlockedItems = [];
  List<CluBlockedItem> _userBlockedItems = [];
  List<CluBlockedItem> _reportedItems = [];
  bool _isLoading = true;
  
  StreamSubscription? _blockedItemsSubscription;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeData();
  }
  
  Future<void> _initializeData() async {
    setState(() => _isLoading = true);
    
    try {
      await CluBlockManager.instance.initialize();
      
      _blockedItemsSubscription = CluBlockManager.instance.blockedItemsStream.listen(
        (items) {
          if (mounted) {
            setState(() {
              _allBlockedItems = items;
              _userBlockedItems = items.where((item) => item.reason == CluBlockReason.userBlocked).toList();
              _reportedItems = items.where((item) => item.reason == CluBlockReason.userReported).toList();
            });
          }
        },
      );
      
      // 初始加载
      final items = CluBlockManager.instance.getAllBlockedItems();
      setState(() {
        _allBlockedItems = items;
        _userBlockedItems = items.where((item) => item.reason == CluBlockReason.userBlocked).toList();
        _reportedItems = items.where((item) => item.reason == CluBlockReason.userReported).toList();
      });
    } catch (e) {
      debugPrint('Failed to initialize block management: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    _blockedItemsSubscription?.cancel();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CluLuxuryTheme.vintageWhite,
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingView() : _buildTabView(),
    );
  }
  
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () => Navigator.of(context).pop(),
        child: const Icon(
          CupertinoIcons.back,
          color: CluLuxuryTheme.deepDenim,
        ),
      ),
      title: const Text(
        'Blocked Content',
        style: TextStyle(
          color: CluLuxuryTheme.deepDenim,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
      actions: [
        if (_allBlockedItems.isNotEmpty)
          CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: _showClearAllDialog,
            child: const Icon(
              CupertinoIcons.trash,
              color: CluLuxuryTheme.dustyRose,
            ),
          ),
      ],
      bottom: TabBar(
        controller: _tabController,
        labelColor: CluLuxuryTheme.deepDenim,
        unselectedLabelColor: CluLuxuryTheme.oliveDrab.withOpacity(0.6),
        indicatorColor: CluLuxuryTheme.warmTaupe,
        tabs: [
          Tab(text: 'All (${_allBlockedItems.length})'),
          Tab(text: 'Blocked (${_userBlockedItems.length})'),
          Tab(text: 'Reported (${_reportedItems.length})'),
        ],
      ),
    );
  }
  
  Widget _buildLoadingView() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(CluLuxuryTheme.warmTaupe),
      ),
    );
  }
  
  Widget _buildTabView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildItemsList(_allBlockedItems),
        _buildItemsList(_userBlockedItems),
        _buildItemsList(_reportedItems),
      ],
    );
  }
  
  Widget _buildItemsList(List<CluBlockedItem> items) {
    if (items.isEmpty) {
      return _buildEmptyView();
    }
    
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: items.length,
      itemBuilder: (context, index) {
        return _buildBlockedItemCard(items[index]);
      },
    );
  }
  
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: CluLuxuryTheme.oliveDrab.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              CupertinoIcons.eye_slash,
              color: CluLuxuryTheme.oliveDrab,
              size: 40,
            ),
          ),
          
          const SizedBox(height: 16),
          
          const Text(
            'No Blocked Content',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: CluLuxuryTheme.deepDenim,
            ),
          ),
          
          const SizedBox(height: 8),
          
          const Text(
            'You haven\'t blocked or reported any content yet',
            style: TextStyle(
              fontSize: 14,
              color: CluLuxuryTheme.oliveDrab,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  Widget _buildBlockedItemCard(CluBlockedItem item) {
    return CluLuxuryCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 缩略图
            _buildThumbnail(item),

            const SizedBox(width: 12),

            // 内容区域
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题和原因
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          item.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: CluLuxuryTheme.deepDenim,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                      const SizedBox(width: 8),

                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: item.reason == CluBlockReason.userReported
                              ? CluLuxuryTheme.dustyRose.withOpacity(0.2)
                              : CluLuxuryTheme.oliveDrab.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          item.reason == CluBlockReason.userReported ? 'Reported' : 'Blocked',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: item.reason == CluBlockReason.userReported
                                ? CluLuxuryTheme.dustyRose
                                : CluLuxuryTheme.oliveDrab,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // 创作者信息
                  Text(
                    'By ${item.creatorName}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: CluLuxuryTheme.oliveDrab,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // 屏蔽时间
                  Text(
                    'Blocked ${_formatBlockedTime(item.blockedAt)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: CluLuxuryTheme.oliveDrab.withOpacity(0.7),
                    ),
                  ),

                  const SizedBox(height: 12),

                  // 操作按钮
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      CluLuxuryButton(
                        text: 'Unblock',
                        onPressed: () => _unblockItem(item),
                        type: CluButtonType.tertiary,
                        size: CluButtonSize.small,
                        width: 80,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThumbnail(CluBlockedItem item) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: CluLuxuryTheme.oliveDrab.withOpacity(0.1),
      ),
      child: FutureBuilder<String?>(
        future: CluThumbnailManager.instance.getThumbnail(item.videoUrl),
        builder: (context, snapshot) {
          if (snapshot.hasData && snapshot.data != null) {
            return ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.file(
                File(snapshot.data!),
                width: 80,
                height: 80,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildPlaceholderThumbnail();
                },
              ),
            );
          } else {
            return _buildPlaceholderThumbnail();
          }
        },
      ),
    );
  }

  Widget _buildPlaceholderThumbnail() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: CluLuxuryTheme.oliveDrab.withOpacity(0.1),
      ),
      child: const Icon(
        CupertinoIcons.videocam,
        color: CluLuxuryTheme.oliveDrab,
        size: 32,
      ),
    );
  }

  Future<void> _unblockItem(CluBlockedItem item) async {
    final confirmed = await showCupertinoDialog<bool>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Unblock Content'),
        content: Text('Are you sure you want to unblock "${item.title}"?'),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          CupertinoDialogAction(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Unblock'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await CluBlockManager.instance.unblockCraft(item.craftId);

        HapticFeedback.lightImpact();
        _showSuccessMessage('Content unblocked successfully');
      } catch (e) {
        _showErrorMessage('Failed to unblock content');
      }
    }
  }

  Future<void> _showClearAllDialog() async {
    final confirmed = await showCupertinoDialog<bool>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Clear All Blocked Content'),
        content: const Text('Are you sure you want to unblock all content? This action cannot be undone.'),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          CupertinoDialogAction(
            onPressed: () => Navigator.of(context).pop(true),
            isDestructiveAction: true,
            child: const Text('Clear All'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await CluBlockManager.instance.clearAllBlocked();

        HapticFeedback.mediumImpact();
        _showSuccessMessage('All blocked content cleared');
      } catch (e) {
        _showErrorMessage('Failed to clear blocked content');
      }
    }
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: CluLuxuryTheme.warmTaupe,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: CluLuxuryTheme.dustyRose,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  String _formatBlockedTime(DateTime time) {
    final now = DateTime.now();
    final diff = now.difference(time);

    if (diff.inDays > 0) {
      return '${diff.inDays} day${diff.inDays > 1 ? 's' : ''} ago';
    } else if (diff.inHours > 0) {
      return '${diff.inHours} hour${diff.inHours > 1 ? 's' : ''} ago';
    } else if (diff.inMinutes > 0) {
      return '${diff.inMinutes} minute${diff.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }
}

// 垃圾代码段1 - 列表动画器
class _ListAnimator {
  static Widget animateListItem(Widget child, int index) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 200 + (index * 50)),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(opacity: value, child: child),
        );
      },
      child: child,
    );
  }
}

// 垃圾代码段2 - 统计计算器
class _StatisticsCalculator {
  static Map<String, int> calculateStats(List<CluBlockedItem> items) {
    final stats = <String, int>{};
    stats['total'] = items.length;
    stats['blocked'] = items.where((item) => item.reason == CluBlockReason.userBlocked).length;
    stats['reported'] = items.where((item) => item.reason == CluBlockReason.userReported).length;
    return stats;
  }

  static String getTopCreator(List<CluBlockedItem> items) {
    if (items.isEmpty) return '';

    final creatorCounts = <String, int>{};
    for (final item in items) {
      creatorCounts[item.creatorName] = (creatorCounts[item.creatorName] ?? 0) + 1;
    }

    return creatorCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }
}
