// Clupa创作发布页面 - CreateCraft
// 支持视频上传、标题内容输入、话题分类选择

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:video_player/video_player.dart';
import 'dart:io';

import '../WeaveRoutes/clu_luxury_theme.dart';
import '../FiberParts/clu_luxury_button.dart';
import '../FiberParts/clu_luxury_card.dart';
import '../ClothVault/clu_user_post_model.dart';
import '../ThreadStore/clu_user_post_manager.dart';
import '../ThreadStore/clu_thumbnail_manager.dart';
import '../ThreadStore/clu_vip_config_manager.dart';
import '../ThreadStore/clu_publish_quota_manager.dart';
import '../ThreadStore/clu_gem_vault_manager.dart';
import '../ThreadStore/clu_app_state.dart';
import '../StitchMaster/clu_needle_actions.dart';
import 'package:flutter_redux/flutter_redux.dart';

class CluCreateCraftView extends StatefulWidget {
  const CluCreateCraftView({super.key});

  @override
  State<CluCreateCraftView> createState() => _CluCreateCraftViewState();
}

class _CluCreateCraftViewState extends State<CluCreateCraftView>
    with AutomaticKeepAliveClientMixin {

  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _contentController = TextEditingController();
  final FocusNode _titleFocusNode = FocusNode();
  final FocusNode _contentFocusNode = FocusNode();

  final ImagePicker _imagePicker = ImagePicker();
  VideoPlayerController? _videoController;

  CluPostCategory? _selectedCategory;
  File? _selectedVideo;
  bool _isPublishing = false;
  bool _isVideoLoading = false;

  String _userAvatar = 'assets/avatars/clu_avatar_01.jpg';
  String _userName = 'Guest User';

  // 金币和免费次数状态
  int _currentGems = 0;
  int _remainingFreePublishes = 0;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
    _initializeManagers();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _titleFocusNode.dispose();
    _contentFocusNode.dispose();
    _videoController?.dispose();
    super.dispose();
  }

  Future<void> _initializeManagers() async {
    try {
      // 初始化发布配额管理器
      await CluPublishQuotaManager.instance.initialize();

      // 初始化金币管理器
      await CluGemVaultManager.instance.initializeVault();

      // 更新状态
      if (mounted) {
        setState(() {
          _currentGems = CluGemVaultManager.instance.currentBalance;
          _remainingFreePublishes = CluPublishQuotaManager.instance.remainingFreePublishes;
        });
      }
    } catch (e) {
      debugPrint('Failed to initialize managers: $e');
    }
  }

  Future<void> _loadUserInfo() async {
    try {
      await CluVipConfigManager.instance.initializeConfig();
      if (mounted) {
        setState(() {
          _userAvatar = CluVipConfigManager.instance.currentAvatar;
          _userName = CluVipConfigManager.instance.currentNickname;
        });
      }
    } catch (e) {
      debugPrint('Failed to load user info: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      backgroundColor: CluLuxuryTheme.vintageWhite,
      appBar: AppBar(
        backgroundColor: CluLuxuryTheme.vintageWhite,
        elevation: 0,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: CluLuxuryTheme.vintageWhite,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
        ),
        title: const Text(
          'Create Post',
          style: TextStyle(
            color: CluLuxuryTheme.deepDenim,
            fontSize: 20,
            fontWeight: FontWeight.w700,
          ),
        ),
        centerTitle: true,
        automaticallyImplyLeading: false,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 用户信息区域
                  _buildUserInfoSection(),

                  const SizedBox(height: 24),

                  // 标题输入
                  _buildTitleInput(),

                  const SizedBox(height: 20),

                  // 内容输入
                  _buildContentInput(),

                  const SizedBox(height: 20),

                  // 视频选择区域
                  _buildVideoSection(),

                  const SizedBox(height: 20),

                  // 话题分类选择
                  _buildCategorySection(),

                  const SizedBox(height: 100), // 为底部按钮留空间
                ],
              ),
            ),
          ),

          // 底部发布按钮
          _buildPublishButton(),
        ],
      ),
    );
  }

  Widget _buildUserInfoSection() {
    return Row(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(25),
            child: Image.asset(
              _userAvatar,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: CluLuxuryTheme.dustyRose.withOpacity(0.2),
                  child: const Icon(
                    CupertinoIcons.person_fill,
                    color: CluLuxuryTheme.dustyRose,
                    size: 24,
                  ),
                );
              },
            ),
          ),
        ),

        const SizedBox(width: 12),

        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _userName,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: CluLuxuryTheme.deepDenim,
                ),
              ),
              const SizedBox(height: 2),
              const Text(
                'Share your craft with the community',
                style: TextStyle(
                  fontSize: 14,
                  color: CluLuxuryTheme.oliveDrab,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTitleInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Title',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: CluLuxuryTheme.deepDenim,
              ),
            ),
            const SizedBox(width: 4),
            const Text(
              '*',
              style: TextStyle(
                fontSize: 16,
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: CluLuxuryTheme.warmTaupe.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: TextField(
            controller: _titleController,
            focusNode: _titleFocusNode,
            decoration: const InputDecoration(
              hintText: 'Give your post a catchy title...',
              hintStyle: TextStyle(
                color: CluLuxuryTheme.oliveDrab,
                fontSize: 16,
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 14,
              ),
            ),
            style: const TextStyle(
              color: CluLuxuryTheme.deepDenim,
              fontSize: 16,
            ),
            maxLength: 100,
            textCapitalization: TextCapitalization.sentences,
            onChanged: (value) {
              setState(() {}); // 触发UI更新
            },
          ),
        ),
      ],
    );
  }

  Widget _buildContentInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Content',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: CluLuxuryTheme.deepDenim,
              ),
            ),
            const SizedBox(width: 4),
            const Text(
              '*',
              style: TextStyle(
                fontSize: 16,
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: CluLuxuryTheme.warmTaupe.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: TextField(
            controller: _contentController,
            focusNode: _contentFocusNode,
            decoration: const InputDecoration(
              hintText: 'Share your crafting story, tips, or inspiration...',
              hintStyle: TextStyle(
                color: CluLuxuryTheme.oliveDrab,
                fontSize: 16,
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 14,
              ),
            ),
            style: const TextStyle(
              color: CluLuxuryTheme.deepDenim,
              fontSize: 16,
              height: 1.4,
            ),
            maxLines: 6,
            maxLength: 1000,
            textCapitalization: TextCapitalization.sentences,
            onChanged: (value) {
              setState(() {}); // 触发UI更新
            },
          ),
        ),
      ],
    );
  }

  Widget _buildVideoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Video',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: CluLuxuryTheme.deepDenim,
              ),
            ),
            const SizedBox(width: 8),
            const Text(
              '*',
              style: TextStyle(
                fontSize: 16,
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        if (_selectedVideo == null) ...[
          _buildVideoSelector(),
        ] else ...[
          _buildVideoPreview(),
        ],
      ],
    );
  }

  Widget _buildVideoSelector() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: CluLuxuryTheme.warmTaupe.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: CluLuxuryTheme.warmTaupe.withOpacity(0.3),
          width: 1,
          style: BorderStyle.solid,
        ),
      ),
      child: _isVideoLoading
          ? const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(CluLuxuryTheme.warmTaupe),
              ),
            )
          : Row(
              children: [
                Expanded(
                  child: CupertinoButton(
                    onPressed: () => _selectVideo(ImageSource.gallery),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          CupertinoIcons.photo_on_rectangle,
                          size: 32,
                          color: CluLuxuryTheme.warmTaupe,
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Gallery',
                          style: TextStyle(
                            color: CluLuxuryTheme.deepDenim,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Container(
                  width: 1,
                  height: 60,
                  color: CluLuxuryTheme.warmTaupe.withOpacity(0.3),
                ),
                Expanded(
                  child: CupertinoButton(
                    onPressed: () => _selectVideo(ImageSource.camera),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          CupertinoIcons.camera,
                          size: 32,
                          color: CluLuxuryTheme.warmTaupe,
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Camera',
                          style: TextStyle(
                            color: CluLuxuryTheme.deepDenim,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildVideoPreview() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        children: [
          if (_videoController != null && _videoController!.value.isInitialized)
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: FittedBox(
                  fit: BoxFit.cover,
                  child: SizedBox(
                    width: _videoController!.value.size.width,
                    height: _videoController!.value.size.height,
                    child: VideoPlayer(_videoController!),
                  ),
                ),
              ),
            )
          else
            const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(CluLuxuryTheme.warmTaupe),
              ),
            ),

          // 删除按钮
          Positioned(
            top: 8,
            right: 8,
            child: CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: _removeVideo,
              child: Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  CupertinoIcons.xmark,
                  color: Colors.white,
                  size: 18,
                ),
              ),
            ),
          ),

          // 播放按钮
          if (_videoController != null && _videoController!.value.isInitialized)
            Center(
              child: CupertinoButton(
                onPressed: _toggleVideoPlayback,
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Icon(
                    _videoController!.value.isPlaying
                        ? CupertinoIcons.pause_fill
                        : CupertinoIcons.play_fill,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCategorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Category',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: CluLuxuryTheme.deepDenim,
              ),
            ),
            const SizedBox(width: 4),
            const Text(
              '*',
              style: TextStyle(
                fontSize: 16,
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: CluPostCategory.values.map((category) {
            final isSelected = _selectedCategory == category;
            return GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                setState(() {
                  _selectedCategory = category;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                decoration: BoxDecoration(
                  color: isSelected
                      ? CluLuxuryTheme.warmTaupe
                      : CluLuxuryTheme.warmTaupe.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected
                        ? CluLuxuryTheme.warmTaupe
                        : CluLuxuryTheme.warmTaupe.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      category.emoji,
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      category.displayName,
                      style: TextStyle(
                        color: isSelected
                            ? CluLuxuryTheme.vintageWhite
                            : CluLuxuryTheme.deepDenim,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildPublishButton() {
    final canPublish = CluPublishQuotaManager.instance.canPublish(_currentGems);
    final buttonText = _isPublishing
        ? 'Publishing...'
        : CluPublishQuotaManager.instance.getPublishButtonText(_currentGems);
    final hintText = CluPublishQuotaManager.instance.getPublishHintText(_currentGems);

    return Container(
      padding: EdgeInsets.fromLTRB(
        20,
        16,
        20,
        16 + MediaQuery.of(context).padding.bottom,
      ),
      decoration: BoxDecoration(
        color: CluLuxuryTheme.vintageWhite,
        border: Border(
          top: BorderSide(
            color: CluLuxuryTheme.warmTaupe.withOpacity(0.2),
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 提示信息
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            margin: const EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              color: CluLuxuryTheme.oliveDrab.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: CluLuxuryTheme.oliveDrab.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  CupertinoIcons.info_circle,
                  size: 16,
                  color: CluLuxuryTheme.oliveDrab,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    hintText,
                    style: TextStyle(
                      fontSize: 13,
                      color: CluLuxuryTheme.oliveDrab,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 发布按钮
          CluLuxuryButton(
            text: buttonText,
            onPressed: (_isPublishing || !canPublish) ? null : _publishPost,
            size: CluButtonSize.large,
            isLoading: _isPublishing,
          ),
        ],
      ),
    );
  }

  // 视频选择
  Future<void> _selectVideo(ImageSource source) async {
    try {
      setState(() {
        _isVideoLoading = true;
      });

      // 检查权限
      final hasPermission = await _checkPermission(source);
      if (!hasPermission) {
        setState(() {
          _isVideoLoading = false;
        });
        return;
      }

      final XFile? pickedFile = await _imagePicker.pickVideo(
        source: source,
        maxDuration: const Duration(minutes: 5), // 最大5分钟
      );

      if (pickedFile != null) {
        final videoFile = File(pickedFile.path);
        await _initializeVideoController(videoFile);

        setState(() {
          _selectedVideo = videoFile;
          _isVideoLoading = false;
        });
      } else {
        setState(() {
          _isVideoLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isVideoLoading = false;
      });
      _showErrorMessage('Failed to select video');
    }
  }

  // 检查权限
  Future<bool> _checkPermission(ImageSource source) async {
    Permission permission;
    String errorMessage;

    if (source == ImageSource.camera) {
      permission = Permission.camera;
      errorMessage = 'No camera privileges';
    } else {
      permission = Permission.photos;
      errorMessage = 'No album access';
    }

    final status = await permission.request();

    if (status.isDenied || status.isPermanentlyDenied) {
      _showErrorMessage(errorMessage);
      return false;
    }

    return status.isGranted;
  }

  // 初始化视频控制器
  Future<void> _initializeVideoController(File videoFile) async {
    try {
      _videoController?.dispose();
      _videoController = VideoPlayerController.file(videoFile);
      await _videoController!.initialize();
      _videoController!.setLooping(true);
    } catch (e) {
      debugPrint('Failed to initialize video controller: $e');
    }
  }

  // 移除视频
  void _removeVideo() {
    HapticFeedback.lightImpact();
    setState(() {
      _selectedVideo = null;
      _videoController?.dispose();
      _videoController = null;
    });
  }

  // 切换视频播放状态
  void _toggleVideoPlayback() {
    if (_videoController == null) return;

    HapticFeedback.lightImpact();
    setState(() {
      if (_videoController!.value.isPlaying) {
        _videoController!.pause();
      } else {
        _videoController!.play();
      }
    });
  }

  // 发布帖子
  Future<void> _publishPost() async {
    // 验证输入
    final errors = CluPostValidator.validatePost(
      title: _titleController.text,
      content: _contentController.text,
      category: _selectedCategory,
      videoPath: _selectedVideo?.path,
    );

    if (errors.isNotEmpty) {
      _showErrorMessage(errors.first);
      return;
    }

    // 检查发布权限
    final publishResult = await _checkPublishPermission();
    if (!publishResult.success) {
      if (publishResult.message.contains('visit the gem store')) {
        _showInsufficientGemsDialog();
      } else {
        _showErrorMessage(publishResult.message);
      }
      return;
    }

    setState(() {
      _isPublishing = true;
    });

    try {
      // 初始化用户帖子管理器
      await CluUserPostManager.instance.initialize();

      // 创建帖子
      final post = CluPostFactory.createPost(
        title: _titleController.text,
        content: _contentController.text,
        authorName: _userName,
        authorAvatar: _userAvatar,
        category: _selectedCategory!,
        videoPath: _selectedVideo!.path ?? '', // 提供默认值
      );

      // 保存帖子
      final success = await CluUserPostManager.instance.addPost(post);

      if (success) {
        // 处理发布成功后的金币扣除和提示
        await _handleSuccessfulPublish(publishResult);

        // 异步生成缩略图，不阻塞UI
        if (post.videoPath != null) {
          _generateThumbnailAsync(post.videoPath!);
        }

        _clearForm();
        _navigateToMainPage();
      } else {
        _showErrorMessage('Failed to publish post');
      }
    } catch (e) {
      _showErrorMessage('Failed to publish post');
      debugPrint('Publish error: $e');
    } finally {
      setState(() {
        _isPublishing = false;
      });
    }
  }

  // 检查发布权限
  Future<PublishResult> _checkPublishPermission() async {
    // 更新当前状态
    _currentGems = CluGemVaultManager.instance.currentBalance;
    _remainingFreePublishes = CluPublishQuotaManager.instance.remainingFreePublishes;

    // 检查是否可以免费发布
    if (CluPublishQuotaManager.instance.canPublishForFree) {
      return PublishResult.success();
    }

    // 检查金币是否足够
    if (_currentGems >= CluPublishQuotaManager.publishCostInGems) {
      return PublishResult.success(
        gemsConsumed: true,
        gemsSpent: CluPublishQuotaManager.publishCostInGems,
      );
    }

    // 金币不足
    return PublishResult.insufficientFunds();
  }

  // 处理成功发布后的逻辑
  Future<void> _handleSuccessfulPublish(PublishResult publishResult) async {
    if (publishResult.gemsConsumed) {
      // 扣除金币
      await CluGemVaultManager.instance.spendGems(publishResult.gemsSpent);

      // 显示金币消费提示
      _showGemConsumedMessage(publishResult.gemsSpent);
    } else {
      // 消费免费次数
      await CluPublishQuotaManager.instance.consumeFreePublish();
    }

    // 更新状态
    setState(() {
      _currentGems = CluGemVaultManager.instance.currentBalance;
      _remainingFreePublishes = CluPublishQuotaManager.instance.remainingFreePublishes;
    });
  }

  // 异步生成缩略图
  void _generateThumbnailAsync(String videoPath) {
    Future.microtask(() async {
      try {
        await CluThumbnailManager.instance.getThumbnail(videoPath);
        debugPrint('缩略图生成成功: $videoPath');
      } catch (e) {
        debugPrint('缩略图生成失败: $e');
      }
    });
  }

  // 清空表单
  void _clearForm() {
    _titleController.clear();
    _contentController.clear();
    setState(() {
      _selectedCategory = null;
      _selectedVideo = null;
      _videoController?.dispose();
      _videoController = null;
    });
  }

  // 显示成功对话框
  void _showSuccessDialog() {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Success!'),
        content: const Text('Your post has been published successfully.'),
        actions: [
          CupertinoDialogAction(
            child: const Text('OK'),
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToMainPage();
            },
          ),
        ],
      ),
    );
  }

  // 显示错误消息
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).padding.bottom + 80,
          left: 16,
          right: 16,
        ),
      ),
    );
  }

  // 显示成功消息
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: CluLuxuryTheme.oliveDrab,
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).padding.bottom + 80,
          left: 16,
          right: 16,
        ),
      ),
    );
  }

  // 导航到主页
  void _navigateToMainPage() {
    // 切换到第一个tab（主页）
    final store = StoreProvider.of<CluAppState>(context);
    store.dispatch(CluChangeTabAction(tabIndex: 0));

    // 显示成功消息
    _showSuccessMessage('Post published successfully!');
  }

  // 显示金币不足对话框
  void _showInsufficientGemsDialog() {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Insufficient Gems'),
        content: Text(
          'You need ${CluPublishQuotaManager.publishCostInGems} gems to publish a video. '
          'You currently have $_currentGems gems.\n\n'
          'Would you like to visit the gem store to purchase more gems?'
        ),
        actions: [
          CupertinoDialogAction(
            child: const Text('Cancel'),
            onPressed: () => Navigator.of(context).pop(),
          ),
          CupertinoDialogAction(
            isDefaultAction: true,
            child: const Text('Visit Store'),
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToGemStore();
            },
          ),
        ],
      ),
    );
  }

  // 显示金币消费提示
  void _showGemConsumedMessage(int gemsSpent) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Post published successfully! $gemsSpent gems consumed.'),
        backgroundColor: CluLuxuryTheme.shimmerGold,
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).padding.bottom + 80,
          left: 16,
          right: 16,
        ),
      ),
    );
  }

  // 导航到金币商城
  void _navigateToGemStore() {
    // 切换到第一个tab（主页），然后导航到金币商城
    final store = StoreProvider.of<CluAppState>(context);
    store.dispatch(CluChangeTabAction(tabIndex: 3)); // 切换到个人档案页

    // 延迟一下再导航到金币商城，确保tab切换完成
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        Navigator.of(context).pushNamed('/gem_store');
      }
    });
  }

  @override
  bool get wantKeepAlive => true;
}
