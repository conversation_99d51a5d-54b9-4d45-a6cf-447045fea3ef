// Clupa创作页面 - CreateCraft
// 拼布创作工具和发布功能

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../WeaveRoutes/clu_luxury_theme.dart';
import '../FiberParts/clu_luxury_button.dart';
import '../FiberParts/clu_luxury_card.dart';

class CluCreateCraftView extends StatefulWidget {
  const CluCreateCraftView({super.key});

  @override
  State<CluCreateCraftView> createState() => _CluCreateCraftViewState();
}

class _CluCreateCraftViewState extends State<CluCreateCraftView>
    with AutomaticKeepAliveClientMixin {

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Container(
      color: CluLuxuryTheme.vintageWhite,
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(
            CluLuxuryTheme.spacing3,
            CluLuxuryTheme.spacing3,
            CluLuxuryTheme.spacing3,
            80, // 为底部导航栏留出空间
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Start Your Patchwork Journey',
                style: Theme.of(context).textTheme.headlineMedium,
              ),

              const SizedBox(height: CluLuxuryTheme.spacing5),

              Expanded(
                child: GridView.count(
                  crossAxisCount: 2,
                  crossAxisSpacing: CluLuxuryTheme.spacing2,
                  mainAxisSpacing: CluLuxuryTheme.spacing2,
                  children: [
                    _buildCreateOption(
                      icon: CupertinoIcons.camera_fill,
                      title: 'Record Video',
                      subtitle: 'Share your process',
                      onTap: () => _showComingSoon('Video Recording'),
                    ),
                    _buildCreateOption(
                      icon: CupertinoIcons.photo_fill,
                      title: 'Upload Photos',
                      subtitle: 'Show your work',
                      onTap: () => _showComingSoon('Photo Upload'),
                    ),
                    _buildCreateOption(
                      icon: CupertinoIcons.sparkles,
                      title: 'AI Design',
                      subtitle: 'Get AI suggestions',
                      onTap: () => _showComingSoon('AI Design Tool'),
                    ),
                    _buildCreateOption(
                      icon: CupertinoIcons.doc_text_fill,
                      title: 'Tutorial',
                      subtitle: 'Create guide',
                      onTap: () => _showComingSoon('Tutorial Creator'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCreateOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return CluLuxuryCard(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 48,
            color: CluLuxuryTheme.warmTaupe,
          ),
          const SizedBox(height: CluLuxuryTheme.spacing2),
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: CluLuxuryTheme.deepDenim,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: CluLuxuryTheme.spacing1),
          Text(
            subtitle,
            style: const TextStyle(
              fontSize: 12,
              color: CluLuxuryTheme.oliveDrab,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showComingSoon(String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$feature Coming Soon!'),
        content: const Text('This feature is under development and will be available in the next update.'),
        actions: [
          CluLuxuryButton(
            text: 'OK',
            onPressed: () => Navigator.of(context).pop(),
            size: CluButtonSize.small,
          ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
