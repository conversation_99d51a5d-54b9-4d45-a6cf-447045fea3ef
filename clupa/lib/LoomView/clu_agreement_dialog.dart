// Clupa协议弹窗
// 显示用户协议和隐私协议的弹窗组件

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';

import '../WeaveRoutes/clu_luxury_theme.dart';
import '../FiberParts/clu_luxury_button.dart';
import '../ThreadStore/clu_agreement_manager.dart';

class CluAgreementDialog extends StatefulWidget {
  final VoidCallback onAccepted;
  final VoidCallback onRejected;

  const CluAgreementDialog({
    super.key,
    required this.onAccepted,
    required this.onRejected,
  });

  @override
  State<CluAgreementDialog> createState() => _CluAgreementDialogState();
}

class _CluAgreementDialogState extends State<CluAgreementDialog> {
  bool _isAccepting = false;

  // 用户协议内容（从TERMS_OF_SERVICE.md转换）
  static const String _termsOfService = '''
 Clupa Terms of Service

Effective Date: August 1, 2025
Contact Email: <EMAIL>

1. Acceptance of Terms

By downloading, installing, or using the Clupa mobile application ("App"), you acknowledge that you have read, understood, and agree to be bound by these Terms of Service ("Terms"). If you do not agree to these Terms, you must not use the App.

2. Service Description

Clupa is a creative community platform focused on fabric upcycling and patchwork design. The App provides video sharing capabilities, AI-powered crafting guidance, and community interaction features. Users can view content, publish their own creative videos, and engage with AI assistants for crafting advice.

3. User Obligations and Conduct

3.1 Account Responsibility
Users are responsible for maintaining the confidentiality of their account information and for all activities that occur under their account. You must provide accurate and complete information when using the App.

3.2 Content Standards
All user-generated content must comply with community guidelines. Users must ensure their content is original, lawful, and does not infringe upon third-party rights.

3.3 Respectful Interaction
Users must interact respectfully with other community members and refrain from harassment, discrimination, or abusive behavior.

4. Prohibited Activities and Zero Tolerance Policy

Clupa maintains a ZERO TOLERANCE policy for inappropriate behavior and content violations. Users are strictly prohibited from:

4.1 Content Violations - Zero Tolerance Enforcement
- Posting content that is illegal, harmful, threatening, abusive, defamatory, or otherwise objectionable
- Sharing content that infringes copyright, trademark, or other intellectual property rights
- Uploading content containing violence, hate speech, or discriminatory material
- We have ZERO TOLERANCE for any form of harassment, bullying, or abusive content

4.2 Platform Misuse - Immediate Action
- Attempting to hack, disrupt, or compromise the App's security or functionality
- Creating multiple accounts to circumvent restrictions or manipulate the platform
- Engaging in spam, fraud, or deceptive practices
- We will NOT TOLERATE any attempts to exploit or abuse our platform

4.3 Commercial Violations - Strict Enforcement
- Using the App for unauthorized commercial purposes without explicit permission
- Selling or transferring accounts to third parties
- ZERO TOLERANCE for unauthorized commercial exploitation of our community

5. Content Ownership and Licensing

5.1 User Content
Users retain ownership of their original content but grant Clupa a non-exclusive license to use, display, and distribute such content within the App for operational purposes.

5.2 Platform Content
All App features, design elements, and proprietary content remain the exclusive property of Clupa and are protected by applicable intellectual property laws.

6. Content Moderation and Zero Tolerance Enforcement

6.1 Reporting Mechanism - Rapid Response
Users can report inappropriate content or behavior through the App's built-in reporting system. ALL reports will be reviewed within 24 hours of submission - NO EXCEPTIONS.

6.2 Content Removal - Immediate Action
Clupa reserves the right to remove any content that violates these Terms WITHOUT PRIOR NOTICE. We have ZERO TOLERANCE for policy violations and will act swiftly to protect our community.

6.3 Account Actions - Strict Consequences
Violations of these Terms will result in IMMEDIATE ACTION:
- Instant content removal
- Immediate temporary account suspension
- Permanent account termination for serious violations
- Legal action where appropriate - we will pursue all available remedies
- NO WARNINGS for severe violations - we maintain ZERO TOLERANCE

7. Service Modifications and Termination

7.1 Service Changes
Clupa reserves the right to modify, suspend, or discontinue any aspect of the App at any time with reasonable notice to users.

7.2 Account Termination
Either party may terminate the user's access to the App at any time. Upon termination, these Terms will remain in effect regarding any content or actions that occurred prior to termination.

8. Disclaimer of Warranties

The App is provided "as is" without warranties of any kind. Clupa disclaims all warranties, express or implied, including but not limited to merchantability, fitness for a particular purpose, and non-infringement.

9. Limitation of Liability

To the maximum extent permitted by law, Clupa shall not be liable for any indirect, incidental, special, or consequential damages arising from the use of the App.

10. Dispute Resolution

Any disputes arising from these Terms shall be resolved through binding arbitration in accordance with applicable laws. Users waive their right to participate in class action lawsuits.

Last Updated: August 1, 2025
For questions or concerns, contact us at: <EMAIL>
''';

  // 隐私协议内容（从PRIVACY_POLICY.md转换）
  static const String _privacyPolicy = '''
 Clupa Privacy Policy

Effective Date: August 1, 2025
Contact Email: <EMAIL>

1. Introduction

This Privacy Policy describes how Clupa ("we," "our," or "us") collects, uses, and protects your personal information when you use our mobile application. We are committed to protecting your privacy and ensuring transparency in our data practices.

2. Your Rights and Choices

2.1 Data Access and Control
You have the right to access, modify, or delete your personal information stored within the App. You can update your profile information, including avatar and nickname, directly through the App settings.

2.2 Content Management
You maintain full control over the content you create and share. You can delete your published videos and manage your interaction history at any time.

2.3 Communication Preferences
You can control how you receive notifications and communications from us through the App's notification settings.

3. Information Collection Practices

3.1 Information You Provide
We collect information you voluntarily provide, including:
- Profile information (nickname, avatar selection)
- Content you create and publish (videos, descriptions, categories)
- Communications with our AI assistant
- Feedback and support requests

3.2 Automatically Collected Information
We automatically collect certain technical information:
- Device identifiers and operating system information
- App usage patterns and feature interactions
- Performance metrics and crash reports
- Local storage data for app functionality

3.3 Video Content Processing
When you upload videos, we process them locally on your device to generate thumbnails and ensure proper playback. Video files are stored locally and are not transmitted to external servers unless explicitly required for app functionality.

4. Information Usage and Purpose

4.1 Core App Functionality
We use your information to:
- Provide video sharing and community features
- Enable AI-powered crafting assistance
- Manage your gem balance and publishing quotas
- Facilitate content discovery and interaction

4.2 Service Improvement
We analyze usage patterns to enhance app performance, develop new features, and improve user experience.

4.3 Communication
We may use your information to send important service updates, respond to support requests, and provide relevant notifications.

5. Data Storage and Security

5.1 Local Storage Priority
Most user data, including published content, preferences, and interaction history, is stored locally on your device using secure storage mechanisms.

5.2 Security Measures
We implement industry-standard security practices including:
- Encrypted local data storage
- Secure API communications
- Regular security assessments
- Access controls and authentication

5.3 Data Retention
Local data is retained until you delete the App or manually clear your data. We do not maintain long-term storage of personal information on external servers.

6. Third-Party Services Integration

6.1 AI Service Provider
We use Moonshot AI services to provide intelligent crafting assistance. Conversations with the AI assistant are processed through their secure API, and we do not store conversation history on external servers.

6.2 Payment Processing
In-app purchases are processed through Apple's App Store, which has its own privacy policies and security measures. We do not directly handle payment information.

6.3 Analytics and Performance
We may use anonymized analytics to understand app performance and user engagement patterns, ensuring no personally identifiable information is shared.

7. Special Scenarios and Protections

7.1 Content Moderation
When content is reported for violations, we may temporarily store relevant information to investigate and resolve the issue. This information is deleted once the matter is resolved.

7.2 Legal Compliance
We may disclose information if required by law, court order, or to protect the rights and safety of our users and the public.

7.3 Business Transitions
In the event of a merger, acquisition, or sale of assets, user information may be transferred as part of the transaction, subject to the same privacy protections.

8. Children's Privacy Protection

The App is not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13. If we become aware that we have collected such information, we will take steps to delete it promptly.

9. Policy Updates and Notifications

We may update this Privacy Policy periodically to reflect changes in our practices or legal requirements. We will notify users of significant changes through the App or other appropriate means.

Last Updated: August 1, 2025
For privacy-related questions or requests, contact us at: <EMAIL>
''';

  void _handleAccept() async {
    if (_isAccepting) return;

    setState(() {
      _isAccepting = true;
    });

    try {
      // 触觉反馈
      HapticFeedback.mediumImpact();

      // 保存同意状态
      final success = await CluAgreementManager.instance.acceptAgreement();
      
      if (success) {
        widget.onAccepted();
      } else {
        _showErrorMessage('Failed to save agreement. Please try again.');
      }
    } catch (e) {
      _showErrorMessage('An error occurred. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isAccepting = false;
        });
      }
    }
  }

  void _handleReject() {
    HapticFeedback.lightImpact();
    widget.onRejected();
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: CluLuxuryTheme.dustyRose,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false, // 防止用户通过返回键关闭弹窗
      child: Dialog(
        insetPadding: const EdgeInsets.all(16),
        backgroundColor: Colors.transparent,
        child: Container(
          width: double.infinity,
          height: MediaQuery.of(context).size.height * 0.85,
          decoration: BoxDecoration(
            color: CluLuxuryTheme.vintageWhite,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            children: [
              // 标题栏
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: CluLuxuryTheme.deepDenim,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      CupertinoIcons.doc_text,
                      color: CluLuxuryTheme.vintageWhite,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'Terms of Service & Privacy Policy',
                        style: TextStyle(
                          color: CluLuxuryTheme.vintageWhite,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 协议内容
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 用户协议
                      Text(
                        _termsOfService,
                        style: const TextStyle(
                          fontSize: 12,
                          height: 1.4,
                          color: CluLuxuryTheme.deepDenim,
                        ),
                      ),
                      
                      const SizedBox(height: 30),
                      
                      // 分隔线
                      Container(
                        height: 1,
                        color: CluLuxuryTheme.oliveDrab.withOpacity(0.3),
                        margin: const EdgeInsets.symmetric(vertical: 20),
                      ),
                      
                      // 隐私协议
                      Text(
                        _privacyPolicy,
                        style: const TextStyle(
                          fontSize: 12,
                          height: 1.4,
                          color: CluLuxuryTheme.deepDenim,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // 按钮区域
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: CluLuxuryTheme.vintageWhite,
                  border: Border(
                    top: BorderSide(
                      color: CluLuxuryTheme.oliveDrab.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: CluLuxuryButton(
                        text: 'Decline',
                        onPressed: _isAccepting ? null : _handleReject,
                        type: CluButtonType.tertiary,
                        size: CluButtonSize.large,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: CluLuxuryButton(
                        text: _isAccepting ? 'Accepting...' : 'Accept',
                        onPressed: _isAccepting ? null : _handleAccept,
                        type: CluButtonType.primary,
                        size: CluButtonSize.large,
                        isLoading: _isAccepting,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
