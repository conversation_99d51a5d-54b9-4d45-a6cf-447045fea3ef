// Clupa视频播放器组件
// 全屏竖屏视频播放，带有交互UI覆盖层

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';

import '../ClothVault/clu_craft_piece_model.dart';
import '../WeaveRoutes/clu_luxury_theme.dart';
import '../FiberParts/clu_luxury_button.dart';

class CluVideoPlayerWidget extends StatefulWidget {
  final CluCraftPiece craftPiece;
  final bool isActive;
  final VoidCallback onLike;
  final VoidCallback onFullscreen;
  final VoidCallback onMore;
  final VoidCallback onProfile;

  const CluVideoPlayerWidget({
    super.key,
    required this.craftPiece,
    required this.isActive,
    required this.onLike,
    required this.onFullscreen,
    required this.onMore,
    required this.onProfile,
  });

  @override
  State<CluVideoPlayerWidget> createState() => _CluVideoPlayerWidgetState();
}

class _CluVideoPlayerWidgetState extends State<CluVideoPlayerWidget>
    with AutomaticKeepAliveClientMixin {
  VideoPlayerController? _controller;
  bool _isInitialized = false;
  bool _isPlaying = false;
  bool _showControls = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  @override
  void didUpdateWidget(CluVideoPlayerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _playVideo();
      } else {
        _pauseVideo();
      }
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  void _initializeVideo() async {
    try {
      // 检查视频路径是否为空或无效
      if (widget.craftPiece.videoUrl.isEmpty) {
        print('视频路径为空，跳过初始化');
        return;
      }

      // 根据路径类型选择合适的控制器
      if (widget.craftPiece.videoUrl.startsWith('assets/')) {
        // Asset视频
        _controller = VideoPlayerController.asset(widget.craftPiece.videoUrl);
      } else if (widget.craftPiece.videoUrl.startsWith('/')) {
        // 本地文件路径
        _controller = VideoPlayerController.file(File(widget.craftPiece.videoUrl));
      } else {
        // 网络视频或其他情况，默认使用asset
        _controller = VideoPlayerController.asset('assets/av/video1.mp4');
      }

      await _controller!.initialize();

      if (mounted) {
        setState(() {
          _isInitialized = true;
        });

        _controller!.setLooping(true);

        if (widget.isActive) {
          _playVideo();
        }
      }
    } catch (e) {
      print('视频初始化失败: $e');
      // 如果初始化失败，尝试使用默认视频
      if (mounted) {
        _initializeDefaultVideo();
      }
    }
  }

  void _initializeDefaultVideo() async {
    try {
      _controller?.dispose();
      _controller = VideoPlayerController.asset('assets/av/video1.mp4');
      await _controller!.initialize();

      if (mounted) {
        setState(() {
          _isInitialized = true;
        });

        _controller!.setLooping(true);

        if (widget.isActive) {
          _playVideo();
        }
      }
    } catch (e) {
      print('默认视频初始化也失败: $e');
    }
  }

  void _playVideo() {
    if (_controller != null && _isInitialized) {
      _controller!.play();
      setState(() {
        _isPlaying = true;
        _showControls = false; // 播放时隐藏控制按钮
      });
    }
  }

  void _pauseVideo() {
    if (_controller != null && _isInitialized) {
      _controller!.pause();
      setState(() {
        _isPlaying = false;
        _showControls = true; // 暂停时显示控制按钮
      });
    }
  }

  // 公共方法：暂停视频播放
  void pauseVideo() {
    _pauseVideo();
  }

  // 公共方法：恢复视频播放
  void resumeVideo() {
    if (widget.isActive) {
      _playVideo();
    }
  }

  void _togglePlayPause() {
    HapticFeedback.lightImpact();
    
    if (_isPlaying) {
      _pauseVideo();
    } else {
      _playVideo();
    }
    
    // 显示控制按钮
    setState(() {
      _showControls = true;
    });
    
    // 3秒后隐藏控制按钮
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _onLikeTapped() {
    HapticFeedback.mediumImpact();
    widget.onLike();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black,
      child: Stack(
        children: [
          // 视频播放器
          _buildVideoPlayer(),
          
          // 点击区域（播放/暂停）
          _buildTapArea(),
          
          // 右侧交互按钮
          _buildInteractionButtons(),
          
          // 底部信息区域
          _buildBottomInfo(),
          
          // 播放控制按钮 - 暂停时始终显示，播放时根据_showControls显示
          if (!_isPlaying || _showControls) _buildPlayButton(),
        ],
      ),
    );
  }

  Widget _buildVideoPlayer() {
    if (!_isInitialized || _controller == null) {
      return Container(
        color: Colors.black,
        child: Center(
          child: widget.craftPiece.thumbnailUrl != null
              ? Image.asset(
                  widget.craftPiece.thumbnailUrl!,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: double.infinity,
                )
              : const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(CluLuxuryTheme.warmTaupe),
                ),
        ),
      );
    }

    // 使用SizedBox.expand确保视频填充整个可用空间
    return SizedBox.expand(
      child: FittedBox(
        fit: BoxFit.cover, // 保持宽高比的同时填充整个空间
        child: SizedBox(
          width: _controller!.value.size.width,
          height: _controller!.value.size.height,
          child: VideoPlayer(_controller!),
        ),
      ),
    );
  }

  Widget _buildTapArea() {
    return Positioned.fill(
      child: GestureDetector(
        onTap: _togglePlayPause,
        behavior: HitTestBehavior.opaque,
        child: Container(color: Colors.transparent),
      ),
    );
  }

  Widget _buildInteractionButtons() {
    // 计算底部导航栏高度 + 安全区域
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    final navBarHeight = 60 + bottomPadding; // 导航栏高度 + 底部安全区域

    return Positioned(
      right: 12,
      bottom: navBarHeight + 30, // 导航栏高度 + 更多间距确保不被遮挡
      child: Column(
        children: [
          // 创作者头像
          GestureDetector(
            onTap: widget.onProfile,
            child: Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    CluLuxuryTheme.warmTaupe.withOpacity(0.8),
                    CluLuxuryTheme.dustyRose.withOpacity(0.9),
                    CluLuxuryTheme.oliveDrab.withOpacity(0.7),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 15,
                    offset: const Offset(0, 4),
                  ),
                  BoxShadow(
                    color: Colors.white.withOpacity(0.6),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Container(
                margin: const EdgeInsets.all(3),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.08),
                      blurRadius: 4,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: ClipOval(
                  child: widget.craftPiece.creatorAvatarUrl != null
                      ? Image.asset(
                          widget.craftPiece.creatorAvatarUrl!,
                          fit: BoxFit.cover,
                        )
                      : Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                CluLuxuryTheme.warmTaupe.withOpacity(0.8),
                                CluLuxuryTheme.dustyRose.withOpacity(0.6),
                              ],
                            ),
                          ),
                          child: const Icon(
                            CupertinoIcons.person_fill,
                            color: Colors.white,
                            size: 26,
                          ),
                        ),
                ),
              ),
            ),
          ),

          const SizedBox(height: 20),
          
          // 点赞按钮
          _buildActionButton(
            icon: widget.craftPiece.isLikedByCurrentVip
                ? CupertinoIcons.heart_fill
                : CupertinoIcons.heart,
            count: widget.craftPiece.likesCount,
            onTap: _onLikeTapped,
            color: widget.craftPiece.isLikedByCurrentVip
                ? CluLuxuryTheme.errorRed
                : Colors.white,
          ),
          
          const SizedBox(height: 20),
          
          // 全屏按钮
          _buildActionButton(
            icon: CupertinoIcons.fullscreen,
            count: null,
            onTap: widget.onFullscreen,
          ),

          const SizedBox(height: 20),

          // 更多功能按钮
          _buildActionButton(
            icon: CupertinoIcons.ellipsis,
            count: null,
            onTap: widget.onMore,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    int? count,
    required VoidCallback onTap,
    Color color = Colors.white,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: onTap,
            child: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withOpacity(0.92),
                border: Border.all(
                  color: CluLuxuryTheme.warmTaupe.withOpacity(0.25),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.12),
                    blurRadius: 12,
                    offset: const Offset(0, 3),
                  ),
                  BoxShadow(
                    color: Colors.white.withOpacity(0.8),
                    blurRadius: 6,
                    offset: const Offset(0, -1),
                  ),
                ],
              ),
              child: Icon(
                icon,
                color: color == Colors.white ? CluLuxuryTheme.deepDenim : color,
                size: 22,
              ),
            ),
          ),
          if (count != null) ...[
            const SizedBox(height: 6),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.65),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 0.5,
                ),
              ),
              child: Text(
                _formatCount(count),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.3,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBottomInfo() {
    // 计算底部导航栏高度 + 安全区域
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    final navBarHeight = 60 + bottomPadding; // 导航栏高度 + 底部安全区域

    return Positioned(
      left: 16,
      right: 85,
      bottom: navBarHeight + 25, // 导航栏高度 + 更多间距确保不被遮挡
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
            colors: [
              Colors.black.withOpacity(0.7),
              Colors.black.withOpacity(0.3),
              Colors.transparent,
            ],
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 创作者名称
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.15),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 0.5,
                ),
              ),
              child: Text(
                '@${widget.craftPiece.creatorDisplayName}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.3,
                ),
              ),
            ),

            const SizedBox(height: 12),

            // 作品标题
            Text(
              widget.craftPiece.title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
                height: 1.3,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),

            const SizedBox(height: 6),

            // 作品描述
            Text(
              widget.craftPiece.description,
              style: TextStyle(
                color: Colors.white.withOpacity(0.85),
                fontSize: 13,
                height: 1.4,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),

            const SizedBox(height: 12),

            // 标签
            Wrap(
              spacing: 6,
              runSpacing: 4,
              children: [
                _buildTag(widget.craftPiece.craftStyle),
                _buildTag(widget.craftPiece.difficultyLevel),
                if (widget.craftPiece.estimatedHours > 0)
                  _buildTag('${widget.craftPiece.estimatedHours}h'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTag(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            CluLuxuryTheme.warmTaupe.withOpacity(0.8),
            CluLuxuryTheme.dustyRose.withOpacity(0.7),
          ],
        ),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(
          color: Colors.white.withOpacity(0.25),
          width: 0.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 11,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.2,
        ),
      ),
    );
  }

  Widget _buildPlayButton() {
    return Center(
      child: GestureDetector(
        onTap: _togglePlayPause,
        child: Container(
          width: 75,
          height: 75,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white.withOpacity(0.95),
            border: Border.all(
              color: CluLuxuryTheme.warmTaupe.withOpacity(0.3),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.15),
                blurRadius: 20,
                offset: const Offset(0, 4),
              ),
              BoxShadow(
                color: Colors.white.withOpacity(0.8),
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Icon(
            _isPlaying ? CupertinoIcons.pause_fill : CupertinoIcons.play_fill,
            color: CluLuxuryTheme.deepDenim,
            size: 35,
          ),
        ),
      ),
    );
  }

  String _formatCount(int? count) {
    if (count == null) {
      return '';
    }
    if (count < 1000) {
      return count.toString();
    } else if (count < 1000000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    }
  }

  @override
  bool get wantKeepAlive => true;
}
