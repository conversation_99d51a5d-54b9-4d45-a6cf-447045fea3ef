// Clupa视频播放器组件
// 全屏竖屏视频播放，带有交互UI覆盖层

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../ClothVault/clu_craft_piece_model.dart';
import '../WeaveRoutes/clu_luxury_theme.dart';
import '../FiberParts/clu_luxury_button.dart';

class CluVideoPlayerWidget extends StatefulWidget {
  final CluCraftPiece craftPiece;
  final bool isActive;
  final VoidCallback onLike;
  final VoidCallback onComment;
  final VoidCallback onShare;
  final VoidCallback onProfile;

  const CluVideoPlayerWidget({
    super.key,
    required this.craftPiece,
    required this.isActive,
    required this.onLike,
    required this.onComment,
    required this.onShare,
    required this.onProfile,
  });

  @override
  State<CluVideoPlayerWidget> createState() => _CluVideoPlayerWidgetState();
}

class _CluVideoPlayerWidgetState extends State<CluVideoPlayerWidget>
    with AutomaticKeepAliveClientMixin {
  VideoPlayerController? _controller;
  bool _isInitialized = false;
  bool _isPlaying = false;
  bool _showControls = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  @override
  void didUpdateWidget(CluVideoPlayerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _playVideo();
      } else {
        _pauseVideo();
      }
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  void _initializeVideo() async {
    try {
      _controller = VideoPlayerController.asset(widget.craftPiece.videoUrl);
      await _controller!.initialize();
      
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
        
        _controller!.setLooping(true);
        
        if (widget.isActive) {
          _playVideo();
        }
      }
    } catch (e) {
      print('视频初始化失败: $e');
    }
  }

  void _playVideo() {
    if (_controller != null && _isInitialized) {
      _controller!.play();
      setState(() {
        _isPlaying = true;
      });
    }
  }

  void _pauseVideo() {
    if (_controller != null && _isInitialized) {
      _controller!.pause();
      setState(() {
        _isPlaying = false;
      });
    }
  }

  void _togglePlayPause() {
    HapticFeedback.lightImpact();
    
    if (_isPlaying) {
      _pauseVideo();
    } else {
      _playVideo();
    }
    
    // 显示控制按钮
    setState(() {
      _showControls = true;
    });
    
    // 3秒后隐藏控制按钮
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _onLikeTapped() {
    HapticFeedback.mediumImpact();
    widget.onLike();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black,
      child: Stack(
        children: [
          // 视频播放器
          _buildVideoPlayer(),
          
          // 点击区域（播放/暂停）
          _buildTapArea(),
          
          // 右侧交互按钮
          _buildInteractionButtons(),
          
          // 底部信息区域
          _buildBottomInfo(),
          
          // 播放控制按钮
          if (_showControls) _buildPlayButton(),
        ],
      ),
    );
  }

  Widget _buildVideoPlayer() {
    if (!_isInitialized || _controller == null) {
      return Container(
        color: Colors.black,
        child: Center(
          child: widget.craftPiece.thumbnailUrl != null
              ? Image.asset(
                  widget.craftPiece.thumbnailUrl!,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: double.infinity,
                )
              : const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(CluLuxuryTheme.warmTaupe),
                ),
        ),
      );
    }

    return Center(
      child: AspectRatio(
        aspectRatio: _controller!.value.aspectRatio,
        child: VideoPlayer(_controller!),
      ),
    );
  }

  Widget _buildTapArea() {
    return Positioned.fill(
      child: GestureDetector(
        onTap: _togglePlayPause,
        behavior: HitTestBehavior.opaque,
        child: Container(color: Colors.transparent),
      ),
    );
  }

  Widget _buildInteractionButtons() {
    return Positioned(
      right: 12,
      bottom: 80, // 调整位置，避免与底部导航栏重叠
      child: Column(
        children: [
          // 创作者头像
          GestureDetector(
            onTap: widget.onProfile,
            child: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 2),
              ),
              child: ClipOval(
                child: widget.craftPiece.creatorAvatarUrl != null
                    ? Image.asset(
                        widget.craftPiece.creatorAvatarUrl!,
                        fit: BoxFit.cover,
                      )
                    : Container(
                        color: CluLuxuryTheme.warmTaupe,
                        child: const Icon(
                          CupertinoIcons.person_fill,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
              ),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // 点赞按钮
          _buildActionButton(
            icon: widget.craftPiece.isLikedByCurrentVip
                ? CupertinoIcons.heart_fill
                : CupertinoIcons.heart,
            count: widget.craftPiece.likesCount,
            onTap: _onLikeTapped,
            color: widget.craftPiece.isLikedByCurrentVip
                ? CluLuxuryTheme.errorRed
                : Colors.white,
          ),
          
          const SizedBox(height: 20),
          
          // 评论按钮
          _buildActionButton(
            icon: CupertinoIcons.chat_bubble,
            count: widget.craftPiece.commentsCount,
            onTap: widget.onComment,
          ),
          
          const SizedBox(height: 20),
          
          // 分享按钮
          _buildActionButton(
            icon: CupertinoIcons.share,
            count: widget.craftPiece.sharesCount,
            onTap: widget.onShare,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required int count,
    required VoidCallback onTap,
    Color color = Colors.white,
  }) {
    return Column(
      children: [
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.black.withOpacity(0.3),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          _formatCount(count),
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomInfo() {
    return Positioned(
      left: 12,
      right: 80,
      bottom: 80, // 调整位置，避免与底部导航栏重叠
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 创作者名称
          Text(
            '@${widget.craftPiece.creatorDisplayName}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // 作品标题
          Text(
            widget.craftPiece.title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 15,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          
          const SizedBox(height: 4),
          
          // 作品描述
          Text(
            widget.craftPiece.description,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          
          const SizedBox(height: 8),
          
          // 标签
          Wrap(
            spacing: 8,
            children: [
              _buildTag(widget.craftPiece.craftStyle),
              _buildTag(widget.craftPiece.difficultyLevel),
              if (widget.craftPiece.estimatedHours > 0)
                _buildTag('${widget.craftPiece.estimatedHours}h'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTag(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildPlayButton() {
    return Center(
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.black.withOpacity(0.5),
        ),
        child: Icon(
          _isPlaying ? CupertinoIcons.pause_fill : CupertinoIcons.play_fill,
          color: Colors.white,
          size: 40,
        ),
      ),
    );
  }

  String _formatCount(int count) {
    if (count < 1000) {
      return count.toString();
    } else if (count < 1000000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    }
  }

  @override
  bool get wantKeepAlive => true;
}
