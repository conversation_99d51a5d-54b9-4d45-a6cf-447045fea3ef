// Clupa个人档案页面 - VipProfile
// 用户信息、作品展示、设置等功能

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:async';
import '../WeaveRoutes/clu_luxury_theme.dart';
import '../FiberParts/clu_luxury_button.dart';
import '../FiberParts/clu_luxury_card.dart';
import '../FiberParts/clu_avatar_picker.dart';
import '../FiberParts/clu_nickname_editor.dart';
import '../ThreadStore/clu_gem_vault_manager.dart';
import '../ThreadStore/clu_vip_config_manager.dart';
import 'clu_gem_bazaar_view.dart';
import 'clu_user_response_view.dart';
import 'clu_privacy_policy_view.dart';
import 'clu_block_management_view.dart';
import 'clu_my_works_view.dart';

class CluVipProfileView extends StatefulWidget {
  const CluVipProfileView({super.key});

  @override
  State<CluVipProfileView> createState() => _CluVipProfileViewState();
}

class _CluVipProfileViewState extends State<CluVipProfileView>
    with AutomaticKeepAliveClientMixin {

  StreamSubscription? _gemBalanceSubscription;
  int _currentGemBalance = 0;

  // 用户配置信息
  String _currentAvatar = 'assets/avatars/clu_avatar_01.jpg';
  String _currentNickname = 'Guest User';

  @override
  void initState() {
    super.initState();
    _initializeGemManager();
    _initializeUserConfig();
    _setupGemBalanceListener();
  }

  Future<void> _initializeGemManager() async {
    await CluGemVaultManager.instance.initializeVault();
    if (mounted) {
      setState(() {
        _currentGemBalance = CluGemVaultManager.instance.currentBalance;
      });
    }
  }

  Future<void> _initializeUserConfig() async {
    await CluVipConfigManager.instance.initializeConfig();
    if (mounted) {
      setState(() {
        _currentAvatar = CluVipConfigManager.instance.currentAvatar;
        _currentNickname = CluVipConfigManager.instance.currentNickname;
      });
    }
  }

  void _setupGemBalanceListener() {
    _gemBalanceSubscription = CluGemVaultManager.instance.gemBalanceStream.listen(
      (balance) {
        if (mounted) {
          setState(() => _currentGemBalance = balance);
        }
      },
    );
  }

  @override
  void dispose() {
    _gemBalanceSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Container(
      color: CluLuxuryTheme.vintageWhite,
      child: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(
            CluLuxuryTheme.spacing3,
            CluLuxuryTheme.spacing3,
            CluLuxuryTheme.spacing3,
            80, // 为底部导航栏留出空间
          ),
          child: Column(
            children: [
              // 顶部设置按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Profile',
                    style: Theme.of(context).textTheme.headlineMedium,
                  ),
                  // IconButton(
                  //   icon: const Icon(CupertinoIcons.gear_alt),
                  //   onPressed: () => _showComingSoon('Settings'),
                  //   color: CluLuxuryTheme.oliveDrab,
                  // ),
                ],
              ),

              const SizedBox(height: CluLuxuryTheme.spacing2),

              // 用户头像和基本信息
              _buildProfileHeader(),
              
              // const SizedBox(height: CluLuxuryTheme.spacing5),
              //
              // // 统计数据
              // _buildStatsRow(),

              const SizedBox(height: CluLuxuryTheme.spacing3),

              // 宝石余额卡片
              _buildGemBalanceCard(),

              const SizedBox(height: CluLuxuryTheme.spacing5),
              
              // 功能菜单
              _buildMenuItems(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Column(
      children: [
        // 头像区域
        Stack(
          children: [
            // 头像
            GestureDetector(
              onTap: _showAvatarPicker,
              child: Container(
                width: 110,
                height: 110,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      CluLuxuryTheme.warmTaupe.withOpacity(0.8),
                      CluLuxuryTheme.dustyRose.withOpacity(0.9),
                      CluLuxuryTheme.oliveDrab.withOpacity(0.7),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.15),
                      blurRadius: 15,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Container(
                  margin: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white,
                  ),
                  child: ClipOval(
                    child: Image.asset(
                      _currentAvatar,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: CluLuxuryTheme.warmTaupe.withOpacity(0.2),
                          child: const Icon(
                            CupertinoIcons.person_fill,
                            size: 50,
                            color: CluLuxuryTheme.warmTaupe,
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),

            // 编辑按钮
            Positioned(
              bottom: 0,
              right: 0,
              child: GestureDetector(
                onTap: _showAvatarPicker,
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: CluLuxuryTheme.warmTaupe,
                    border: Border.all(
                      color: Colors.white,
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 4,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: const Icon(
                    CupertinoIcons.camera_fill,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: CluLuxuryTheme.spacing2),

        // 用户名区域
        GestureDetector(
          onTap: _showNicknameEditor,
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 8,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: CluLuxuryTheme.warmTaupe.withOpacity(0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _currentNickname,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: CluLuxuryTheme.deepDenim,
                  ),
                ),
                const SizedBox(width: 8),
                const Icon(
                  CupertinoIcons.pencil,
                  size: 16,
                  color: CluLuxuryTheme.oliveDrab,
                ),
              ],
            ),
          ),
        ),

        // const SizedBox(height: CluLuxuryTheme.spacing1),
        //
        // // 等级标签
        // Container(
        //   padding: const EdgeInsets.symmetric(
        //     horizontal: CluLuxuryTheme.spacing2,
        //     vertical: CluLuxuryTheme.spacing1,
        //   ),
        //   decoration: BoxDecoration(
        //     gradient: LinearGradient(
        //       begin: Alignment.topLeft,
        //       end: Alignment.bottomRight,
        //       colors: [
        //         CluLuxuryTheme.dustyRose.withOpacity(0.8),
        //         CluLuxuryTheme.warmTaupe.withOpacity(0.6),
        //       ],
        //     ),
        //     borderRadius: BorderRadius.circular(CluLuxuryTheme.smallRadius),
        //     boxShadow: [
        //       BoxShadow(
        //         color: Colors.black.withOpacity(0.1),
        //         blurRadius: 4,
        //         offset: const Offset(0, 1),
        //       ),
        //     ],
        //   ),
        //   child: const Text(
        //     'Beginner Crafter',
        //     style: TextStyle(
        //       fontSize: 12,
        //       fontWeight: FontWeight.w600,
        //       color: Colors.white,
        //     ),
        //   ),
        // ),
      ],
    );
  }

  // Widget _buildStatsRow() {
  //   return Row(
  //     children: [
  //       Expanded(child: _buildStatItem('Projects', '0')),
  //       Expanded(child: _buildStatItem('Likes', '0')),
  //       Expanded(child: _buildStatItem('Followers', '0')),
  //     ],
  //   );
  // }

  Widget _buildGemBalanceCard() {
    return CluLuxuryCard(
      onTap: () {
        debugPrint('Gem balance card tapped!');
        _navigateToGemBazaar();
      },
      child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                CluLuxuryTheme.warmTaupe,
                CluLuxuryTheme.dustyRose,
              ],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              // 宝石图标
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  CupertinoIcons.star_fill,
                  color: Colors.white,
                  size: 24,
                ),
              ),

              const SizedBox(width: 16),

              // 余额信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'My Gems',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _currentGemBalance.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              // 箭头图标
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  CupertinoIcons.chevron_right,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ],
          ),
        ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return CluLuxuryCard(
      child: Column(
        children: [
          Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w700,
              color: CluLuxuryTheme.deepDenim,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: CluLuxuryTheme.oliveDrab,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItems() {
    return Column(
      children: [
        // _buildMenuItem(
        //   icon: CupertinoIcons.photo_on_rectangle,
        //   title: 'My Projects',
        //   subtitle: 'View your patchwork creations',
        //   onTap: () => _showComingSoon('My Projects'),
        // ),
        //
        // const SizedBox(height: CluLuxuryTheme.spacing2),
        //
        // _buildMenuItem(
        //   icon: CupertinoIcons.heart_fill,
        //   title: 'Liked Projects',
        //   subtitle: 'Projects you\'ve liked',
        //   onTap: () => _showComingSoon('Liked Projects'),
        // ),
        //
        // const SizedBox(height: CluLuxuryTheme.spacing2),
        //
        // _buildMenuItem(
        //   icon: CupertinoIcons.bookmark_fill,
        //   title: 'Saved Tutorials',
        //   subtitle: 'Your bookmarked guides',
        //   onTap: () => _showComingSoon('Saved Tutorials'),
        // ),
        //
        // const SizedBox(height: CluLuxuryTheme.spacing2),
        //
        // _buildMenuItem(
        //   icon: CupertinoIcons.shopping_cart,
        //   title: 'Purchase History',
        //   subtitle: 'View your purchases',
        //   onTap: () => _showComingSoon('Purchase History'),
        // ),
        //
        // const SizedBox(height: CluLuxuryTheme.spacing2),

        _buildMenuItem(
          icon: CupertinoIcons.videocam_fill,
          title: 'My Works',
          subtitle: 'View and manage your videos',
          onTap: _navigateToMyWorks,
        ),

        const SizedBox(height: CluLuxuryTheme.spacing2),

        _buildMenuItem(
          icon: CupertinoIcons.eye_slash,
          title: 'Blocked Content',
          subtitle: 'Manage blocked and reported videos',
          onTap: _navigateToBlockManagement,
        ),

        const SizedBox(height: CluLuxuryTheme.spacing2),

        _buildMenuItem(
          icon: CupertinoIcons.chat_bubble_text,
          title: 'Share Thoughts',
          subtitle: 'Help us improve the app',
          onTap: _navigateToUserResponse,
        ),

        const SizedBox(height: CluLuxuryTheme.spacing2),

        _buildMenuItem(
          icon: CupertinoIcons.doc_text,
          title: 'Privacy Policy',
          subtitle: 'View our privacy terms',
          onTap: _navigateToPrivacyPolicy,
        ),

        // const SizedBox(height: CluLuxuryTheme.spacing5),
        //
        // // 登录按钮
        // CluLuxuryButton(
        //   text: 'Sign In',
        //   onPressed: () => _showComingSoon('Sign In'),
        //   width: double.infinity,
        //   icon: CupertinoIcons.person_circle,
        // ),
      ],
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return CluLuxuryCard(
      onTap: onTap,
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: CluLuxuryTheme.warmTaupe.withOpacity(0.1),
              borderRadius: BorderRadius.circular(CluLuxuryTheme.smallRadius),
            ),
            child: Icon(
              icon,
              size: 24,
              color: CluLuxuryTheme.warmTaupe,
            ),
          ),
          
          const SizedBox(width: CluLuxuryTheme.spacing2),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: CluLuxuryTheme.deepDenim,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 14,
                    color: CluLuxuryTheme.oliveDrab,
                  ),
                ),
              ],
            ),
          ),
          
          const Icon(
            CupertinoIcons.chevron_right,
            size: 20,
            color: CluLuxuryTheme.oliveDrab,
          ),
        ],
      ),
    );
  }

  void _navigateToGemBazaar() {
    debugPrint('Attempting to navigate to gem bazaar...');
    try {
      Navigator.of(context).push(
        CupertinoPageRoute(
          builder: (context) {
            debugPrint('Building CluGemBazaarView...');
            return const CluGemBazaarView();
          },
        ),
      );
    } catch (e) {
      debugPrint('Navigation to gem bazaar failed: $e');
      // 显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Unable to open gem store: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _navigateToUserResponse() {
    Navigator.of(context).push(
      CupertinoPageRoute(
        builder: (context) => const CluUserResponseView(),
      ),
    );
  }

  void _navigateToPrivacyPolicy() {
    Navigator.of(context).push(
      CupertinoPageRoute(
        builder: (context) => const CluPrivacyPolicyView(),
      ),
    );
  }

  void _navigateToMyWorks() {
    Navigator.of(context).push(
      CupertinoPageRoute(
        builder: (context) => const CluMyWorksView(),
      ),
    );
  }

  void _navigateToBlockManagement() {
    Navigator.of(context).push(
      CupertinoPageRoute(
        builder: (context) => const CluBlockManagementView(),
      ),
    ).then((result) {
      // 如果从屏蔽管理页面返回并有变更，可以在这里处理
      if (result == true) {
        // 可以刷新相关数据或显示提示
      }
    });
  }

  void _showAvatarPicker() {
    showAvatarPicker(
      context: context,
      currentAvatar: _currentAvatar,
      onAvatarSelected: (String newAvatar) async {
        final success = await CluVipConfigManager.instance.updateAvatar(newAvatar);
        if (success && mounted) {
          setState(() {
            _currentAvatar = newAvatar;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Avatar updated successfully!'),
              backgroundColor: CluLuxuryTheme.warmTaupe,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
    );
  }

  void _showNicknameEditor() {
    showNicknameEditor(
      context: context,
      currentNickname: _currentNickname,
      onNicknameChanged: (String newNickname) async {
        final success = await CluVipConfigManager.instance.updateNickname(newNickname);
        if (success && mounted) {
          setState(() {
            _currentNickname = newNickname;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Nickname updated successfully!'),
              backgroundColor: CluLuxuryTheme.warmTaupe,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
    );
  }

  void _showComingSoon(String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$feature Coming Soon!'),
        content: const Text('This feature will be available in the next update.'),
        actions: [
          CluLuxuryButton(
            text: 'OK',
            onPressed: () => Navigator.of(context).pop(),
            size: CluButtonSize.small,
          ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
