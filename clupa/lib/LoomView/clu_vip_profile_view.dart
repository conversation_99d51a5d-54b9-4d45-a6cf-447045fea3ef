// Clupa个人档案页面 - VipProfile
// 用户信息、作品展示、设置等功能

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../WeaveRoutes/clu_luxury_theme.dart';
import '../FiberParts/clu_luxury_button.dart';
import '../FiberParts/clu_luxury_card.dart';

class CluVipProfileView extends StatefulWidget {
  const CluVipProfileView({super.key});

  @override
  State<CluVipProfileView> createState() => _CluVipProfileViewState();
}

class _CluVipProfileViewState extends State<CluVipProfileView>
    with AutomaticKeepAliveClientMixin {

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Scaffold(
      backgroundColor: CluLuxuryTheme.vintageWhite,
      appBar: AppBar(
        title: const Text('Profile'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(CupertinoIcons.gear_alt),
            onPressed: () => _showComingSoon('Settings'),
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(CluLuxuryTheme.spacing3),
          child: Column(
            children: [
              // 用户头像和基本信息
              _buildProfileHeader(),
              
              const SizedBox(height: CluLuxuryTheme.spacing5),
              
              // 统计数据
              _buildStatsRow(),
              
              const SizedBox(height: CluLuxuryTheme.spacing5),
              
              // 功能菜单
              _buildMenuItems(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Column(
      children: [
        // 头像
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: CluLuxuryTheme.warmTaupe.withOpacity(0.2),
            border: Border.all(
              color: CluLuxuryTheme.warmTaupe,
              width: 3,
            ),
          ),
          child: const Icon(
            CupertinoIcons.person_fill,
            size: 50,
            color: CluLuxuryTheme.warmTaupe,
          ),
        ),
        
        const SizedBox(height: CluLuxuryTheme.spacing2),
        
        // 用户名
        Text(
          'Guest User',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        
        const SizedBox(height: CluLuxuryTheme.spacing1),
        
        // 等级标签
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: CluLuxuryTheme.spacing2,
            vertical: CluLuxuryTheme.spacing1,
          ),
          decoration: BoxDecoration(
            color: CluLuxuryTheme.dustyRose.withOpacity(0.2),
            borderRadius: BorderRadius.circular(CluLuxuryTheme.smallRadius),
          ),
          child: const Text(
            'Beginner Crafter',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: CluLuxuryTheme.deepDenim,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatsRow() {
    return Row(
      children: [
        Expanded(child: _buildStatItem('Projects', '0')),
        Expanded(child: _buildStatItem('Likes', '0')),
        Expanded(child: _buildStatItem('Followers', '0')),
      ],
    );
  }

  Widget _buildStatItem(String label, String value) {
    return CluLuxuryCard(
      child: Column(
        children: [
          Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w700,
              color: CluLuxuryTheme.deepDenim,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: CluLuxuryTheme.oliveDrab,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItems() {
    return Column(
      children: [
        _buildMenuItem(
          icon: CupertinoIcons.photo_on_rectangle,
          title: 'My Projects',
          subtitle: 'View your patchwork creations',
          onTap: () => _showComingSoon('My Projects'),
        ),
        
        const SizedBox(height: CluLuxuryTheme.spacing2),
        
        _buildMenuItem(
          icon: CupertinoIcons.heart_fill,
          title: 'Liked Projects',
          subtitle: 'Projects you\'ve liked',
          onTap: () => _showComingSoon('Liked Projects'),
        ),
        
        const SizedBox(height: CluLuxuryTheme.spacing2),
        
        _buildMenuItem(
          icon: CupertinoIcons.bookmark_fill,
          title: 'Saved Tutorials',
          subtitle: 'Your bookmarked guides',
          onTap: () => _showComingSoon('Saved Tutorials'),
        ),
        
        const SizedBox(height: CluLuxuryTheme.spacing2),
        
        _buildMenuItem(
          icon: CupertinoIcons.shopping_cart,
          title: 'Purchase History',
          subtitle: 'View your purchases',
          onTap: () => _showComingSoon('Purchase History'),
        ),
        
        const SizedBox(height: CluLuxuryTheme.spacing5),
        
        // 登录按钮
        CluLuxuryButton(
          text: 'Sign In',
          onPressed: () => _showComingSoon('Sign In'),
          width: double.infinity,
          icon: CupertinoIcons.person_circle,
        ),
      ],
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return CluLuxuryCard(
      onTap: onTap,
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: CluLuxuryTheme.warmTaupe.withOpacity(0.1),
              borderRadius: BorderRadius.circular(CluLuxuryTheme.smallRadius),
            ),
            child: Icon(
              icon,
              size: 24,
              color: CluLuxuryTheme.warmTaupe,
            ),
          ),
          
          const SizedBox(width: CluLuxuryTheme.spacing2),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: CluLuxuryTheme.deepDenim,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 14,
                    color: CluLuxuryTheme.oliveDrab,
                  ),
                ),
              ],
            ),
          ),
          
          const Icon(
            CupertinoIcons.chevron_right,
            size: 20,
            color: CluLuxuryTheme.oliveDrab,
          ),
        ],
      ),
    );
  }

  void _showComingSoon(String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$feature Coming Soon!'),
        content: const Text('This feature will be available in the next update.'),
        actions: [
          CluLuxuryButton(
            text: 'OK',
            onPressed: () => Navigator.of(context).pop(),
            size: CluButtonSize.small,
          ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
