// Clupa我的作品页面
// 显示用户发布的所有作品，支持查看、删除等操作

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'dart:io';

import '../WeaveRoutes/clu_luxury_theme.dart';
import '../FiberParts/clu_luxury_card.dart';
import '../FiberParts/clu_luxury_button.dart';
import '../ThreadStore/clu_user_post_manager.dart';
import '../ThreadStore/clu_thumbnail_manager.dart';
import '../ClothVault/clu_user_post_model.dart';
import '../ClothVault/clu_craft_piece_model.dart';
import 'clu_fullscreen_video_view.dart';

class CluMyWorksView extends StatefulWidget {
  const CluMyWorksView({super.key});

  @override
  State<CluMyWorksView> createState() => _CluMyWorksViewState();
}

class _CluMyWorksViewState extends State<CluMyWorksView> {
  List<CluUserPost> _userPosts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserPosts();
  }

  Future<void> _loadUserPosts() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final posts = CluUserPostManager.instance.getAllPosts();
      
      // 为每个帖子生成缩略图
      for (final post in posts) {
        if (post.videoPath.isNotEmpty) {
          await CluThumbnailManager.instance.getThumbnail(post.videoPath);
        }
      }

      setState(() {
        _userPosts = posts;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Failed to load user posts: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshPosts() async {
    await _loadUserPosts();
  }

  void _openFullscreenVideo(CluUserPost post) async {
    // 将用户帖子转换为CluCraftPiece
    final craftPiece = post.toCraftPieceData();
    
    final result = await Navigator.of(context).push(
      CupertinoPageRoute(
        builder: (context) => CluFullscreenVideoView(
          craftPiece: craftPiece,
        ),
      ),
    );

    // 如果视频被删除，刷新列表
    if (result == 'deleted') {
      await _refreshPosts();
    }
  }

  String _formatPostTime(DateTime postTime) {
    final now = DateTime.now();
    final difference = now.difference(postTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CluLuxuryTheme.vintageWhite,
      appBar: AppBar(
        backgroundColor: CluLuxuryTheme.vintageWhite,
        elevation: 0,
        title: const Text(
          'My Works',
          style: TextStyle(
            color: CluLuxuryTheme.deepDenim,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(
            CupertinoIcons.back,
            color: CluLuxuryTheme.deepDenim,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(
              CupertinoIcons.refresh,
              color: CluLuxuryTheme.deepDenim,
            ),
            onPressed: _refreshPosts,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CupertinoActivityIndicator(
          color: CluLuxuryTheme.deepDenim,
        ),
      );
    }

    if (_userPosts.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _refreshPosts,
      color: CluLuxuryTheme.deepDenim,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _userPosts.length,
        itemBuilder: (context, index) {
          return _buildPostCard(_userPosts[index]);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            CupertinoIcons.videocam,
            size: 80,
            color: CluLuxuryTheme.oliveDrab.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No works yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: CluLuxuryTheme.oliveDrab.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first video to see it here',
            style: TextStyle(
              fontSize: 14,
              color: CluLuxuryTheme.oliveDrab.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPostCard(CluUserPost post) {
    return CluLuxuryCard(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _openFullscreenVideo(post),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 缩略图
              _buildThumbnail(post),
              
              const SizedBox(width: 16),
              
              // 内容区域
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题
                    Text(
                      post.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: CluLuxuryTheme.deepDenim,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // 描述
                    Text(
                      post.content,
                      style: TextStyle(
                        fontSize: 14,
                        color: CluLuxuryTheme.oliveDrab.withOpacity(0.8),
                        height: 1.4,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // 底部信息
                    Row(
                      children: [
                        // 点赞数
                        Icon(
                          post.isLiked ? CupertinoIcons.heart_fill : CupertinoIcons.heart,
                          size: 16,
                          color: post.isLiked ? CluLuxuryTheme.dustyRose : CluLuxuryTheme.oliveDrab,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${post.likeCount}',
                          style: TextStyle(
                            fontSize: 12,
                            color: CluLuxuryTheme.oliveDrab.withOpacity(0.7),
                          ),
                        ),
                        
                        const SizedBox(width: 16),
                        
                        // 发布时间
                        Icon(
                          CupertinoIcons.time,
                          size: 16,
                          color: CluLuxuryTheme.oliveDrab.withOpacity(0.7),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _formatPostTime(post.createdAt),
                          style: TextStyle(
                            fontSize: 12,
                            color: CluLuxuryTheme.oliveDrab.withOpacity(0.7),
                          ),
                        ),
                        
                        const Spacer(),
                        
                        // 播放图标
                        Icon(
                          CupertinoIcons.play_circle,
                          size: 20,
                          color: CluLuxuryTheme.deepDenim.withOpacity(0.7),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildThumbnail(CluUserPost post) {
    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: CluLuxuryTheme.oliveDrab.withOpacity(0.1),
      ),
      child: FutureBuilder<String?>(
        future: post.videoPath != null
            ? CluThumbnailManager.instance.getThumbnail(post.videoPath!)
            : Future.value(null),
        builder: (context, snapshot) {
          if (snapshot.hasData && snapshot.data != null) {
            return ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.file(
                File(snapshot.data!),
                width: 100,
                height: 100,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildPlaceholderThumbnail();
                },
              ),
            );
          } else {
            return _buildPlaceholderThumbnail();
          }
        },
      ),
    );
  }

  Widget _buildPlaceholderThumbnail() {
    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: CluLuxuryTheme.oliveDrab.withOpacity(0.1),
      ),
      child: const Icon(
        CupertinoIcons.videocam,
        color: CluLuxuryTheme.oliveDrab,
        size: 40,
      ),
    );
  }
}
