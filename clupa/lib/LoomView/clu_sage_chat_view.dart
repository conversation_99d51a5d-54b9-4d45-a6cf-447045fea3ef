// Clupa AI聊天页面
// 支持不同AI模式的智能对话界面

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';

import '../ClothVault/clu_sage_chat_model.dart';
import '../PatternSage/clu_sage_chat_service.dart';
import '../WeaveRoutes/clu_luxury_theme.dart';
import '../FiberParts/clu_luxury_button.dart';
import '../ThreadStore/clu_vip_config_manager.dart';

class CluSageChatView extends StatefulWidget {
  final CluSageMode mode;

  const CluSageChatView({
    super.key,
    required this.mode,
  });

  @override
  State<CluSageChatView> createState() => _CluSageChatViewState();
}

class _CluSageChatViewState extends State<CluSageChatView>
    with TickerProviderStateMixin {
  final CluSageChatService _chatService = CluSageChatService();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  String _currentMessage = '';
  String _userAvatar = 'assets/avatars/clu_avatar_01.jpg';

  CluChatHistory? _chatHistory;
  bool _isLoading = false;
  bool _isSending = false;
  late AnimationController _typingAnimationController;

  @override
  void initState() {
    super.initState();
    _typingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();
    _loadChatHistory();
    _loadUserAvatar();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _typingAnimationController.dispose();
    super.dispose();
  }

  Future<void> _loadChatHistory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final history = await _chatService.getChatHistory(widget.mode);
      setState(() {
        _chatHistory = history;
        _isLoading = false;
      });
      _scrollToBottom();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadUserAvatar() async {
    try {
      await CluVipConfigManager.instance.initializeConfig();
      setState(() {
        _userAvatar = CluVipConfigManager.instance.currentAvatar;
      });
    } catch (e) {
      // 使用默认头像
      debugPrint('Failed to load user avatar: $e');
    }
  }

  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty || _isSending || _chatHistory == null) return;

    HapticFeedback.lightImpact();
    _messageController.clear();

    setState(() {
      _isSending = true;
    });

    try {
      // 添加用户消息
      final updatedHistory = await _chatService.addMessage(_chatHistory!, message, true);
      setState(() {
        _chatHistory = updatedHistory;
      });
      _scrollToBottom();

      // 获取AI回复
      final aiResponse = await _chatService.sendMessage(
        message,
        widget.mode,
        _chatHistory!.messages,
      );

      // 添加AI回复
      final finalHistory = await _chatService.addMessage(_chatHistory!, aiResponse, false);
      setState(() {
        _chatHistory = finalHistory;
        _isSending = false;
      });
      _scrollToBottom();
    } catch (e) {
      setState(() {
        _isSending = false;
      });
      _showErrorSnackBar('Failed to send message. Please try again.');
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _clearChatHistory() async {
    HapticFeedback.mediumImpact();
    
    final confirmed = await showCupertinoDialog<bool>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Clear Chat History'),
        content: const Text('Are you sure you want to clear all chat history for this mode? This action cannot be undone.'),
        actions: [
          CupertinoDialogAction(
            child: const Text('Cancel'),
            onPressed: () => Navigator.of(context).pop(false),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            child: const Text('Clear'),
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _chatService.clearChatHistory(widget.mode);
      await _loadChatHistory();
      _showSuccessSnackBar('Chat history cleared successfully.');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: CluLuxuryTheme.oliveDrab,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CluLuxuryTheme.vintageWhite,
      appBar: AppBar(
        backgroundColor: CluLuxuryTheme.vintageWhite,
        elevation: 0,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: CluLuxuryTheme.vintageWhite,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
        ),
        leading: CupertinoButton(
          padding: EdgeInsets.zero,
          child: const Icon(
            CupertinoIcons.back,
            color: CluLuxuryTheme.deepDenim,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Column(
          children: [
            Text(
              widget.mode.displayName,
              style: const TextStyle(
                color: CluLuxuryTheme.deepDenim,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              'AI Assistant',
              style: const TextStyle(
                color: CluLuxuryTheme.oliveDrab,
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
        centerTitle: true,
        actions: [
          CupertinoButton(
            padding: const EdgeInsets.only(right: 16),
            child: const Icon(
              CupertinoIcons.trash,
              color: CluLuxuryTheme.dustyRose,
              size: 22,
            ),
            onPressed: _clearChatHistory,
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: _buildChatArea(),
          ),
          _buildInputArea(),
        ],
      ),
    );
  }

  Widget _buildChatArea() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(CluLuxuryTheme.warmTaupe),
        ),
      );
    }

    if (_chatHistory == null || _chatHistory!.messages.isEmpty) {
      return const Center(
        child: Text(
          'No messages yet',
          style: TextStyle(
            color: CluLuxuryTheme.oliveDrab,
            fontSize: 16,
          ),
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _chatHistory!.messages.length + (_isSending ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _chatHistory!.messages.length && _isSending) {
          return _buildTypingIndicator();
        }
        
        final message = _chatHistory!.messages[index];
        return _buildMessageBubble(message);
      },
    );
  }

  Widget _buildMessageBubble(CluChatMessage message) {
    final isUser = message.isUser;
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: CluLuxuryTheme.warmTaupe.withOpacity(0.2),
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Icon(
                CupertinoIcons.sparkles,
                size: 18,
                color: CluLuxuryTheme.warmTaupe,
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isUser 
                    ? CluLuxuryTheme.deepDenim 
                    : CluLuxuryTheme.warmTaupe.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                message.content,
                style: TextStyle(
                  color: isUser 
                      ? CluLuxuryTheme.vintageWhite 
                      : CluLuxuryTheme.deepDenim,
                  fontSize: 16,
                  height: 1.4,
                ),
              ),
            ),
          ),
          if (isUser) ...[
            const SizedBox(width: 8),
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Image.asset(
                  _userAvatar,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: CluLuxuryTheme.dustyRose.withOpacity(0.2),
                      child: const Icon(
                        CupertinoIcons.person_fill,
                        size: 18,
                        color: CluLuxuryTheme.dustyRose,
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: CluLuxuryTheme.warmTaupe.withOpacity(0.2),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(
              CupertinoIcons.sparkles,
              size: 18,
              color: CluLuxuryTheme.warmTaupe,
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: CluLuxuryTheme.warmTaupe.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: AnimatedBuilder(
              animation: _typingAnimationController,
              builder: (context, child) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: List.generate(3, (index) {
                    final delay = index * 0.2;
                    final animationValue = (_typingAnimationController.value - delay).clamp(0.0, 1.0);
                    final opacity = (animationValue * 2).clamp(0.0, 1.0);
                    
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: Container(
                        width: 6,
                        height: 6,
                        decoration: BoxDecoration(
                          color: CluLuxuryTheme.warmTaupe.withOpacity(opacity),
                          borderRadius: BorderRadius.circular(3),
                        ),
                      ),
                    );
                  }),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputArea() {
    return Container(
      padding: EdgeInsets.fromLTRB(
        16,
        8,
        16,
        16 + MediaQuery.of(context).padding.bottom,
      ),
      decoration: BoxDecoration(
        color: CluLuxuryTheme.vintageWhite,
        border: Border(
          top: BorderSide(
            color: CluLuxuryTheme.warmTaupe.withOpacity(0.2),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: CluLuxuryTheme.warmTaupe.withOpacity(0.1),
                borderRadius: BorderRadius.circular(24),
              ),
              child: TextField(
                controller: _messageController,
                decoration: const InputDecoration(
                  hintText: 'Ask me anything...',
                  hintStyle: TextStyle(
                    color: CluLuxuryTheme.oliveDrab,
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                ),
                style: const TextStyle(
                  color: CluLuxuryTheme.deepDenim,
                  fontSize: 16,
                ),
                maxLines: null,
                textCapitalization: TextCapitalization.sentences,
                onChanged: (value) {
                  setState(() {
                    _currentMessage = value;
                  });
                },
                onSubmitted: (_) => _sendMessage(),
              ),
            ),
          ),
          const SizedBox(width: 8),
          CupertinoButton(
            padding: EdgeInsets.zero,
            child: Container(
              width: 44,
              height: 44,
              decoration: BoxDecoration(
                color: _currentMessage.trim().isNotEmpty && !_isSending
                    ? CluLuxuryTheme.deepDenim
                    : CluLuxuryTheme.oliveDrab.withOpacity(0.3),
                borderRadius: BorderRadius.circular(22),
              ),
              child: Icon(
                CupertinoIcons.paperplane_fill,
                color: CluLuxuryTheme.vintageWhite,
                size: 20,
              ),
            ),
            onPressed: _sendMessage,
          ),
        ],
      ),
    );
  }
}
