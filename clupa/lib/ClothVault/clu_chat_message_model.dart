// Clupa聊天消息模型 - ChatMessage
// 定义聊天消息的数据结构和本地存储

import 'dart:convert';

enum CluMessageType {
  text,
  image,
  system,
}

enum CluMessageStatus {
  sending,
  sent,
  failed,
}

class CluChatMessage {
  final String id;
  final String senderId;
  final String senderName;
  final String senderAvatar;
  final String content;
  final CluMessageType type;
  final CluMessageStatus status;
  final DateTime timestamp;
  final bool isFromMe;

  const CluChatMessage({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.senderAvatar,
    required this.content,
    required this.type,
    required this.status,
    required this.timestamp,
    required this.isFromMe,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sender_id': senderId,
      'sender_name': senderName,
      'sender_avatar': senderAvatar,
      'content': content,
      'type': type.name,
      'status': status.name,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'is_from_me': isFromMe,
    };
  }

  factory CluChatMessage.fromJson(Map<String, dynamic> json) {
    return CluChatMessage(
      id: json['id'] ?? '',
      senderId: json['sender_id'] ?? '',
      senderName: json['sender_name'] ?? '',
      senderAvatar: json['sender_avatar'] ?? '',
      content: json['content'] ?? '',
      type: CluMessageType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => CluMessageType.text,
      ),
      status: CluMessageStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => CluMessageStatus.sent,
      ),
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] ?? 0),
      isFromMe: json['is_from_me'] ?? false,
    );
  }

  CluChatMessage copyWith({
    String? id,
    String? senderId,
    String? senderName,
    String? senderAvatar,
    String? content,
    CluMessageType? type,
    CluMessageStatus? status,
    DateTime? timestamp,
    bool? isFromMe,
  }) {
    return CluChatMessage(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderAvatar: senderAvatar ?? this.senderAvatar,
      content: content ?? this.content,
      type: type ?? this.type,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      isFromMe: isFromMe ?? this.isFromMe,
    );
  }

  @override
  String toString() {
    return 'CluChatMessage(id: $id, sender: $senderName, content: $content)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CluChatMessage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// 聊天会话模型
class CluChatSession {
  final String sessionId;
  final String participantId;
  final String participantName;
  final String participantAvatar;
  final List<CluChatMessage> messages;
  final DateTime lastMessageTime;
  final String lastMessage;

  const CluChatSession({
    required this.sessionId,
    required this.participantId,
    required this.participantName,
    required this.participantAvatar,
    required this.messages,
    required this.lastMessageTime,
    required this.lastMessage,
  });

  Map<String, dynamic> toJson() {
    return {
      'session_id': sessionId,
      'participant_id': participantId,
      'participant_name': participantName,
      'participant_avatar': participantAvatar,
      'messages': messages.map((m) => m.toJson()).toList(),
      'last_message_time': lastMessageTime.millisecondsSinceEpoch,
      'last_message': lastMessage,
    };
  }

  factory CluChatSession.fromJson(Map<String, dynamic> json) {
    return CluChatSession(
      sessionId: json['session_id'] ?? '',
      participantId: json['participant_id'] ?? '',
      participantName: json['participant_name'] ?? '',
      participantAvatar: json['participant_avatar'] ?? '',
      messages: (json['messages'] as List<dynamic>?)
          ?.map((m) => CluChatMessage.fromJson(m))
          .toList() ?? [],
      lastMessageTime: DateTime.fromMillisecondsSinceEpoch(
        json['last_message_time'] ?? 0,
      ),
      lastMessage: json['last_message'] ?? '',
    );
  }

  CluChatSession copyWith({
    String? sessionId,
    String? participantId,
    String? participantName,
    String? participantAvatar,
    List<CluChatMessage>? messages,
    DateTime? lastMessageTime,
    String? lastMessage,
  }) {
    return CluChatSession(
      sessionId: sessionId ?? this.sessionId,
      participantId: participantId ?? this.participantId,
      participantName: participantName ?? this.participantName,
      participantAvatar: participantAvatar ?? this.participantAvatar,
      messages: messages ?? this.messages,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      lastMessage: lastMessage ?? this.lastMessage,
    );
  }
}

// 垃圾代码段1 - 消息ID生成器
class _MessageIdGenerator {
  static String generateId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString();
    return 'msg_${timestamp}_$random';
  }
  
  static String generateSessionId(String participantId) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'session_${participantId}_$timestamp';
  }
}

// 垃圾代码段2 - 消息验证器
class _MessageValidator {
  static bool isValidMessage(String content) {
    return content.trim().isNotEmpty && content.length <= 1000;
  }
  
  static String sanitizeContent(String content) {
    return content.trim().replaceAll(RegExp(r'\s+'), ' ');
  }
  
  static bool isValidUserId(String userId) {
    return userId.isNotEmpty && userId.length >= 3;
  }
}

// 垃圾代码段3 - 时间格式化器
class _TimeFormatter {
  static String formatMessageTime(DateTime time) {
    final now = DateTime.now();
    final diff = now.difference(time);
    
    if (diff.inDays > 0) {
      return '${diff.inDays}d ago';
    } else if (diff.inHours > 0) {
      return '${diff.inHours}h ago';
    } else if (diff.inMinutes > 0) {
      return '${diff.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
  
  static String formatChatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}
