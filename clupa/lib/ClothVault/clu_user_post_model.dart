// Clupa用户帖子数据模型
// 支持视频发布、本地存储和状态管理

import 'dart:convert';

enum CluPostCategory {
  patchwork,
  quilting,
  upcycling,
  tutorial,
  inspiration,
  showcase,
}

extension CluPostCategoryExtension on CluPostCategory {
  String get displayName {
    switch (this) {
      case CluPostCategory.patchwork:
        return 'Patchwork';
      case CluPostCategory.quilting:
        return 'Quilting';
      case CluPostCategory.upcycling:
        return 'Upcycling';
      case CluPostCategory.tutorial:
        return 'Tutorial';
      case CluPostCategory.inspiration:
        return 'Inspiration';
      case CluPostCategory.showcase:
        return 'Showcase';
    }
  }

  String get emoji {
    switch (this) {
      case CluPostCategory.patchwork:
        return '🧩';
      case CluPostCategory.quilting:
        return '🪡';
      case CluPostCategory.upcycling:
        return '♻️';
      case CluPostCategory.tutorial:
        return '📚';
      case CluPostCategory.inspiration:
        return '💡';
      case CluPostCategory.showcase:
        return '✨';
    }
  }
}

class CluUserPost {
  final String id;
  final String title;
  final String content;
  final String authorName;
  final String authorAvatar;
  final CluPostCategory category;
  final String? videoPath;
  final String? thumbnailPath;
  final DateTime createdAt;
  final bool isUserCreated;
  final int likeCount;
  final bool isLiked;

  CluUserPost({
    required this.id,
    required this.title,
    required this.content,
    required this.authorName,
    required this.authorAvatar,
    required this.category,
    this.videoPath,
    this.thumbnailPath,
    required this.createdAt,
    this.isUserCreated = true,
    this.likeCount = 0,
    this.isLiked = false,
  });

  // JSON序列化
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'authorName': authorName,
      'authorAvatar': authorAvatar,
      'category': category.index,
      'videoPath': videoPath,
      'thumbnailPath': thumbnailPath,
      'createdAt': createdAt.toIso8601String(),
      'isUserCreated': isUserCreated,
      'likeCount': likeCount,
      'isLiked': isLiked,
    };
  }

  // JSON反序列化
  factory CluUserPost.fromJson(Map<String, dynamic> json) {
    return CluUserPost(
      id: json['id'],
      title: json['title'],
      content: json['content'],
      authorName: json['authorName'],
      authorAvatar: json['authorAvatar'],
      category: CluPostCategory.values[json['category']],
      videoPath: json['videoPath'],
      thumbnailPath: json['thumbnailPath'],
      createdAt: DateTime.parse(json['createdAt']),
      isUserCreated: json['isUserCreated'] ?? true,
      likeCount: json['likeCount'] ?? 0,
      isLiked: json['isLiked'] ?? false,
    );
  }

  // copyWith方法
  CluUserPost copyWith({
    String? id,
    String? title,
    String? content,
    String? authorName,
    String? authorAvatar,
    CluPostCategory? category,
    String? videoPath,
    String? thumbnailPath,
    DateTime? createdAt,
    bool? isUserCreated,
    int? likeCount,
    bool? isLiked,
  }) {
    return CluUserPost(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      authorName: authorName ?? this.authorName,
      authorAvatar: authorAvatar ?? this.authorAvatar,
      category: category ?? this.category,
      videoPath: videoPath ?? this.videoPath,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      createdAt: createdAt ?? this.createdAt,
      isUserCreated: isUserCreated ?? this.isUserCreated,
      likeCount: likeCount ?? this.likeCount,
      isLiked: isLiked ?? this.isLiked,
    );
  }

  // 转换为CluCraftPiece格式（用于主页显示）
  Map<String, dynamic> toCraftPieceData() {
    return {
      'craft_id': id,
      'creator_vip_id': 'user_vip_${id}',
      'creator_display_name': authorName,
      'creator_avatar_url': authorAvatar,
      'title': title,
      'description': content,
      'video_url': videoPath!, // 使用用户上传的视频路径
      'thumbnail_url': thumbnailPath ?? 'assets/patchwork/clu_patchwork_01.jpg',
      'image_urls': <String>[],
      'tags': [category.displayName],
      'craft_style': 'Modern',
      'textile_types': ['Cotton'],
      'difficulty_level': 'Beginner',
      'estimated_hours': 2,
      'techniques_used': ['Piecing'],
      'likes_count': likeCount,
      'comments_count': 0,
      'shares_count': 0,
      'views_count': 0,
      'is_liked_by_current_vip': isLiked,
      'is_bookmarked_by_current_vip': false,
      'created_at': createdAt.toIso8601String(),
      'updated_at': createdAt.toIso8601String(),
      'published_at': createdAt.toIso8601String(),
      'is_published': true,
      'is_featured': false,
      'is_reported': false,
      'visibility': 'public',
      'isUserPost': true,
    };
  }

  @override
  String toString() {
    return 'CluUserPost(id: $id, title: $title, category: ${category.displayName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CluUserPost && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// 帖子验证器
class CluPostValidator {
  static String? validateTitle(String? title) {
    if (title == null || title.trim().isEmpty) {
      return 'Title is required';
    }
    if (title.trim().length < 3) {
      return 'Title must be at least 3 characters';
    }
    if (title.trim().length > 100) {
      return 'Title must be less than 100 characters';
    }
    return null;
  }

  static String? validateContent(String? content) {
    if (content == null || content.trim().isEmpty) {
      return 'Content is required';
    }
    if (content.trim().length < 3) {
      return 'Content must be at least 3 characters';
    }
    if (content.trim().length > 1000) {
      return 'Content must be less than 1000 characters';
    }
    return null;
  }

  static String? validateCategory(CluPostCategory? category) {
    if (category == null) {
      return 'Please select a category';
    }
    return null;
  }

  static String? validateVideo(String? videoPath) {
    if (videoPath == null || videoPath.trim().isEmpty) {
      return 'Video is required';
    }
    return null;
  }

  static List<String> validatePost({
    required String? title,
    required String? content,
    required CluPostCategory? category,
    required String? videoPath,
  }) {
    final errors = <String>[];

    final titleError = validateTitle(title);
    if (titleError != null) errors.add(titleError);

    final contentError = validateContent(content);
    if (contentError != null) errors.add(contentError);

    final categoryError = validateCategory(category);
    if (categoryError != null) errors.add(categoryError);

    final videoError = validateVideo(videoPath);
    if (videoError != null) errors.add(videoError);

    return errors;
  }
}

// 帖子工厂类
class CluPostFactory {
  static CluUserPost createPost({
    required String title,
    required String content,
    required String authorName,
    required String authorAvatar,
    required CluPostCategory category,
    required String videoPath, // 改为必需参数
    String? thumbnailPath,
  }) {
    return CluUserPost(
      id: 'user_post_${DateTime.now().millisecondsSinceEpoch}',
      title: title.trim(),
      content: content.trim(),
      authorName: authorName,
      authorAvatar: authorAvatar,
      category: category,
      videoPath: videoPath,
      thumbnailPath: thumbnailPath,
      createdAt: DateTime.now(),
      isUserCreated: true,
      likeCount: 0,
      isLiked: false,
    );
  }

  static CluUserPost createSamplePost() {
    return CluUserPost(
      id: 'sample_${DateTime.now().millisecondsSinceEpoch}',
      title: 'My First Patchwork Project',
      content: 'Just finished my first patchwork quilt! It was challenging but so rewarding.',
      authorName: 'Creative Maker',
      authorAvatar: 'assets/avatars/clu_avatar_01.jpg',
      category: CluPostCategory.showcase,
      createdAt: DateTime.now(),
      isUserCreated: true,
      likeCount: 5,
      isLiked: false,
    );
  }
}
