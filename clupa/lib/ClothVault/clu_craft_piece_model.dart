// Clupa作品模型 - CraftPiece
// 避免使用post等常规命名，使用CraftPiece体现手工艺品特色

import 'package:meta/meta.dart';

@immutable
class CluCraftPiece {
  final String craftId;
  final String creatorVipId;
  final String creatorDisplayName;
  final String? creatorAvatarUrl;
  
  // 作品内容
  final String title;
  final String description;
  final String videoUrl;
  final String? thumbnailUrl;
  final List<String> imageUrls;
  final List<String> tags;
  
  // 拼布技术信息
  final String craftStyle; // "Traditional", "Modern", "Art Quilt", etc.
  final List<String> textileTypes; // "Cotton", "Linen", "Silk", etc.
  final String difficultyLevel; // "Beginner", "Intermediate", "Advanced"
  final int estimatedHours;
  final List<String> techniquesUsed; // "Piecing", "Applique", "Embroidery", etc.
  
  // 社交数据
  final int likesCount;
  final int commentsCount;
  final int sharesCount;
  final int viewsCount;
  final bool isLikedByCurrentVip;
  final bool isBookmarkedByCurrentVip;
  
  // 时间信息
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? publishedAt;
  
  // 状态信息
  final bool isPublished;
  final bool isFeatured;
  final bool isReported;
  final String visibility; // "public", "followers", "private"

  const CluCraftPiece({
    required this.craftId,
    required this.creatorVipId,
    required this.creatorDisplayName,
    this.creatorAvatarUrl,
    required this.title,
    required this.description,
    required this.videoUrl,
    this.thumbnailUrl,
    this.imageUrls = const [],
    this.tags = const [],
    required this.craftStyle,
    this.textileTypes = const [],
    required this.difficultyLevel,
    this.estimatedHours = 0,
    this.techniquesUsed = const [],
    this.likesCount = 0,
    this.commentsCount = 0,
    this.sharesCount = 0,
    this.viewsCount = 0,
    this.isLikedByCurrentVip = false,
    this.isBookmarkedByCurrentVip = false,
    required this.createdAt,
    this.updatedAt,
    this.publishedAt,
    this.isPublished = false,
    this.isFeatured = false,
    this.isReported = false,
    this.visibility = "public",
  });

  // 从JSON创建实例
  factory CluCraftPiece.fromJson(Map<String, dynamic> json) {
    return CluCraftPiece(
      craftId: json['craft_id'] as String,
      creatorVipId: json['creator_vip_id'] as String,
      creatorDisplayName: json['creator_display_name'] as String,
      creatorAvatarUrl: json['creator_avatar_url'] as String?,
      title: json['title'] as String,
      description: json['description'] as String,
      videoUrl: json['video_url'] as String,
      thumbnailUrl: json['thumbnail_url'] as String?,
      imageUrls: List<String>.from(json['image_urls'] ?? []),
      tags: List<String>.from(json['tags'] ?? []),
      craftStyle: json['craft_style'] as String,
      textileTypes: List<String>.from(json['textile_types'] ?? []),
      difficultyLevel: json['difficulty_level'] as String,
      estimatedHours: json['estimated_hours'] as int? ?? 0,
      techniquesUsed: List<String>.from(json['techniques_used'] ?? []),
      likesCount: json['likes_count'] as int? ?? 0,
      commentsCount: json['comments_count'] as int? ?? 0,
      sharesCount: json['shares_count'] as int? ?? 0,
      viewsCount: json['views_count'] as int? ?? 0,
      isLikedByCurrentVip: json['is_liked_by_current_vip'] as bool? ?? false,
      isBookmarkedByCurrentVip: json['is_bookmarked_by_current_vip'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      publishedAt: json['published_at'] != null 
          ? DateTime.parse(json['published_at'] as String)
          : null,
      isPublished: json['is_published'] as bool? ?? false,
      isFeatured: json['is_featured'] as bool? ?? false,
      isReported: json['is_reported'] as bool? ?? false,
      visibility: json['visibility'] as String? ?? "public",
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'craft_id': craftId,
      'creator_vip_id': creatorVipId,
      'creator_display_name': creatorDisplayName,
      'creator_avatar_url': creatorAvatarUrl,
      'title': title,
      'description': description,
      'video_url': videoUrl,
      'thumbnail_url': thumbnailUrl,
      'image_urls': imageUrls,
      'tags': tags,
      'craft_style': craftStyle,
      'textile_types': textileTypes,
      'difficulty_level': difficultyLevel,
      'estimated_hours': estimatedHours,
      'techniques_used': techniquesUsed,
      'likes_count': likesCount,
      'comments_count': commentsCount,
      'shares_count': sharesCount,
      'views_count': viewsCount,
      'is_liked_by_current_vip': isLikedByCurrentVip,
      'is_bookmarked_by_current_vip': isBookmarkedByCurrentVip,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'published_at': publishedAt?.toIso8601String(),
      'is_published': isPublished,
      'is_featured': isFeatured,
      'is_reported': isReported,
      'visibility': visibility,
    };
  }

  // 复制并修改
  CluCraftPiece copyWith({
    String? craftId,
    String? creatorVipId,
    String? creatorDisplayName,
    String? creatorAvatarUrl,
    String? title,
    String? description,
    String? videoUrl,
    String? thumbnailUrl,
    List<String>? imageUrls,
    List<String>? tags,
    String? craftStyle,
    List<String>? textileTypes,
    String? difficultyLevel,
    int? estimatedHours,
    List<String>? techniquesUsed,
    int? likesCount,
    int? commentsCount,
    int? sharesCount,
    int? viewsCount,
    bool? isLikedByCurrentVip,
    bool? isBookmarkedByCurrentVip,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? publishedAt,
    bool? isPublished,
    bool? isFeatured,
    bool? isReported,
    String? visibility,
  }) {
    return CluCraftPiece(
      craftId: craftId ?? this.craftId,
      creatorVipId: creatorVipId ?? this.creatorVipId,
      creatorDisplayName: creatorDisplayName ?? this.creatorDisplayName,
      creatorAvatarUrl: creatorAvatarUrl ?? this.creatorAvatarUrl,
      title: title ?? this.title,
      description: description ?? this.description,
      videoUrl: videoUrl ?? this.videoUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      imageUrls: imageUrls ?? this.imageUrls,
      tags: tags ?? this.tags,
      craftStyle: craftStyle ?? this.craftStyle,
      textileTypes: textileTypes ?? this.textileTypes,
      difficultyLevel: difficultyLevel ?? this.difficultyLevel,
      estimatedHours: estimatedHours ?? this.estimatedHours,
      techniquesUsed: techniquesUsed ?? this.techniquesUsed,
      likesCount: likesCount ?? this.likesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      sharesCount: sharesCount ?? this.sharesCount,
      viewsCount: viewsCount ?? this.viewsCount,
      isLikedByCurrentVip: isLikedByCurrentVip ?? this.isLikedByCurrentVip,
      isBookmarkedByCurrentVip: isBookmarkedByCurrentVip ?? this.isBookmarkedByCurrentVip,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      publishedAt: publishedAt ?? this.publishedAt,
      isPublished: isPublished ?? this.isPublished,
      isFeatured: isFeatured ?? this.isFeatured,
      isReported: isReported ?? this.isReported,
      visibility: visibility ?? this.visibility,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CluCraftPiece &&
          runtimeType == other.runtimeType &&
          craftId == other.craftId;

  @override
  int get hashCode => craftId.hashCode;

  @override
  String toString() {
    return 'CluCraftPiece{craftId: $craftId, title: $title, creator: $creatorDisplayName}';
  }
}
