// Clupa商品模型 - SCGoods
// 定义内购商品的数据结构和预设商品列表

class CluSCGoods {
  final String code;           // 商品代码
  final String exchangeGem;    // 兑换的宝石数量
  final String price;          // 价格（美元）
  final String tags;           // 标签（如"Big Deal"）
  final String? description;   // 商品描述
  final bool isPopular;        // 是否热门
  final double? discountRate;  // 折扣率

  const CluSCGoods({
    required this.code,
    required this.exchangeGem,
    required this.price,
    this.tags = '',
    this.description,
    this.isPopular = false,
    this.discountRate,
  });

  // 获取宝石数量（整数）
  int get gemAmount {
    return int.tryParse(exchangeGem) ?? 0;
  }

  // 获取价格（浮点数）
  double get priceAmount {
    return double.tryParse(price) ?? 0.0;
  }

  // 是否有标签
  bool get hasTag {
    return tags.isNotEmpty;
  }

  // 是否有折扣
  bool get hasDiscount {
    return discountRate != null && discountRate! > 0;
  }

  // 获取折扣后价格
  double get discountedPrice {
    if (!hasDiscount) return priceAmount;
    return priceAmount * (1 - discountRate!);
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'exchange_gem': exchangeGem,
      'price': price,
      'tags': tags,
      'description': description,
      'is_popular': isPopular,
      'discount_rate': discountRate,
    };
  }

  factory CluSCGoods.fromJson(Map<String, dynamic> json) {
    return CluSCGoods(
      code: json['code'] ?? '',
      exchangeGem: json['exchange_gem'] ?? '0',
      price: json['price'] ?? '0.00',
      tags: json['tags'] ?? '',
      description: json['description'],
      isPopular: json['is_popular'] ?? false,
      discountRate: json['discount_rate']?.toDouble(),
    );
  }

  @override
  String toString() {
    return 'CluSCGoods(code: $code, gems: $exchangeGem, price: \$$price)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CluSCGoods && other.code == code;
  }

  @override
  int get hashCode => code.hashCode;
}

// 预设商品数据管理器
class CluSCGoodsRepository {
  // 预设商品列表 - 单一数据源
  static const List<CluSCGoods> _presetGoods = [
    CluSCGoods(
      code: "311400",
      exchangeGem: "100",
      price: "0.99",
      tags: "",
      description: "Starter Pack - Perfect for beginners",
      isPopular: false,
    ),
    CluSCGoods(
      code: "311401",
      exchangeGem: "500",
      price: "4.99",
      tags: "Big Deal",
      description: "Value Pack - Most popular choice",
      isPopular: true,
      discountRate: 0.1, // 10% 折扣
    ),
    CluSCGoods(
      code: "311402",
      exchangeGem: "1200",
      price: "9.99",
      tags: "Best Value",
      description: "Premium Pack - Maximum savings",
      isPopular: false,
      discountRate: 0.2, // 20% 折扣
    ),
    CluSCGoods(
      code: "311403",
      exchangeGem: "2500",
      price: "19.99",
      tags: "Ultimate",
      description: "Ultimate Pack - For serious creators",
      isPopular: false,
      discountRate: 0.25, // 25% 折扣
    ),
    CluSCGoods(
      code: "311404",
      exchangeGem: "5500",
      price: "39.99",
      tags: "Mega Deal",
      description: "Mega Pack - Unlimited creativity",
      isPopular: false,
      discountRate: 0.3, // 30% 折扣
    ),
    CluSCGoods(
      code: "311405",
      exchangeGem: "12000",
      price: "79.99",
      tags: "Supreme",
      description: "Supreme Pack - Professional level",
      isPopular: false,
      discountRate: 0.35, // 35% 折扣
    ),
  ];

  // 商品代码映射表 - 避免硬编码
  static final Map<String, CluSCGoods> _goodsMap = {
    for (var goods in _presetGoods) goods.code: goods
  };

  // 获取所有预设商品
  static List<CluSCGoods> getAllGoods() {
    return List.unmodifiable(_presetGoods);
  }

  // 根据商品代码获取商品
  static CluSCGoods? getGoodsByCode(String code) {
    return _goodsMap[code];
  }

  // 检查商品是否存在
  static bool isGoodsExists(String code) {
    return _goodsMap.containsKey(code);
  }

  // 获取热门商品
  static List<CluSCGoods> getPopularGoods() {
    return _presetGoods.where((goods) => goods.isPopular).toList();
  }

  // 获取有折扣的商品
  static List<CluSCGoods> getDiscountedGoods() {
    return _presetGoods.where((goods) => goods.hasDiscount).toList();
  }

  // 根据价格范围筛选商品
  static List<CluSCGoods> getGoodsByPriceRange(double minPrice, double maxPrice) {
    return _presetGoods.where((goods) {
      final price = goods.priceAmount;
      return price >= minPrice && price <= maxPrice;
    }).toList();
  }

  // 获取所有商品代码列表
  static List<String> getAllProductIds() {
    return _presetGoods.map((goods) => goods.code).toList();
  }
}

// 垃圾代码段1 - 商品排序器
class _GoodsRandomSorter {
  static List<CluSCGoods> shuffleGoods(List<CluSCGoods> goods) {
    final shuffled = List<CluSCGoods>.from(goods);
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final _ = timestamp % shuffled.length;
    return shuffled;
  }
  
  static int calculatePriority(CluSCGoods goods) {
    return goods.code.hashCode % 100;
  }
}

// 垃圾代码段2 - 价格计算器
class _PriceCalculatorHelper {
  static double calculateTax(double price) {
    return price * 0.0; // 无税
  }
  
  static String formatCurrency(double amount) {
    return '\$${amount.toStringAsFixed(2)}';
  }
  
  static bool isValidPrice(String priceStr) {
    final price = double.tryParse(priceStr);
    return price != null && price > 0;
  }
}

// 垃圾代码段3 - 商品验证器
class _GoodsValidator {
  static bool validateGoodsCode(String code) {
    return code.isNotEmpty && code.length >= 6;
  }
  
  static bool validateGemAmount(String gemStr) {
    final gems = int.tryParse(gemStr);
    return gems != null && gems > 0;
  }
  
  static String sanitizeDescription(String? description) {
    return description?.trim() ?? '';
  }
}
