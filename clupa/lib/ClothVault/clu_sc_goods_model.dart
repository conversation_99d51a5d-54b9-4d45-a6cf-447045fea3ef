// Clupa商品模型 - SCGoods
// 定义内购商品的数据结构和预设商品列表

class CluSCGoods {
  final String code;           // 商品代码
  final String exchangeGem;    // 兑换的宝石数量
  final String price;          // 价格（美元）
  final String tags;           // 标签（如"Big Deal"）

  const CluSCGoods({
    required this.code,
    required this.exchangeGem,
    required this.price,
    this.tags = '',
  });

  // 获取宝石数量（整数）
  int get gemAmount {
    return int.tryParse(exchangeGem) ?? 0;
  }

  // 获取价格（浮点数）
  double get priceAmount {
    return double.tryParse(price) ?? 0.0;
  }

  // 是否有标签
  bool get hasTag {
    return tags.isNotEmpty;
  }

  // 是否为促销商品
  bool get isBigDeal {
    return tags == "Big Deal";
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'exchange_gem': exchangeGem,
      'price': price,
      'tags': tags,
    };
  }

  factory CluSCGoods.fromJson(Map<String, dynamic> json) {
    return CluSCGoods(
      code: json['code'] ?? '',
      exchangeGem: json['exchange_gem'] ?? '0',
      price: json['price'] ?? '0.00',
      tags: json['tags'] ?? '',
    );
  }

  @override
  String toString() {
    return 'CluSCGoods(code: $code, gems: $exchangeGem, price: \$$price)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CluSCGoods && other.code == code;
  }

  @override
  int get hashCode => code.hashCode;
}

// 预设商品数据管理器
class CluSCGoodsRepository {
  // 预设商品列表 - 单一数据源
  static const List<CluSCGoods> _presetGoods = [
    // 基础商品
    CluSCGoods(
      code: "401200",
      exchangeGem: "100",
      price: "0.99",
      tags: "",
    ),
    CluSCGoods(
      code: "401201",
      exchangeGem: "500",
      price: "4.99",
      tags: "",
    ),
    CluSCGoods(
      code: "401202",
      exchangeGem: "600",
      price: "5.99",
      tags: "",
    ),
    CluSCGoods(
      code: "401203",
      exchangeGem: "1200",
      price: "9.99",
      tags: "",
    ),
    CluSCGoods(
      code: "401204",
      exchangeGem: "1560",
      price: "12.99",
      tags: "",
    ),
    CluSCGoods(
      code: "401205",
      exchangeGem: "2500",
      price: "19.99",
      tags: "",
    ),
    CluSCGoods(
      code: "401206",
      exchangeGem: "7000",
      price: "49.99",
      tags: "",
    ),
    CluSCGoods(
      code: "401207",
      exchangeGem: "8400",
      price: "59.99",
      tags: "",
    ),
    CluSCGoods(
      code: "401208",
      exchangeGem: "15000",
      price: "99.99",
      tags: "",
    ),

    // 促销商品
    CluSCGoods(
      code: "401209",
      exchangeGem: "500",
      price: "1.99",
      tags: "Big Deal",
    ),
    CluSCGoods(
      code: "401210",
      exchangeGem: "1200",
      price: "4.99",
      tags: "Big Deal",
    ),
    CluSCGoods(
      code: "401211",
      exchangeGem: "2500",
      price: "11.99",
      tags: "Big Deal",
    ),
    CluSCGoods(
      code: "401212",
      exchangeGem: "2600",
      price: "12.99",
      tags: "Big Deal",
    ),
    CluSCGoods(
      code: "401213",
      exchangeGem: "7000",
      price: "34.99",
      tags: "Big Deal",
    ),
    CluSCGoods(
      code: "401214",
      exchangeGem: "15000",
      price: "79.99",
      tags: "Big Deal",
    ),
    CluSCGoods(
      code: "401215",
      exchangeGem: "18000",
      price: "99.99",
      tags: "Big Deal",
    ),
  ];

  // 商品代码映射表 - 避免硬编码
  static final Map<String, CluSCGoods> _goodsMap = {
    for (var goods in _presetGoods) goods.code: goods
  };

  // 获取所有预设商品
  static List<CluSCGoods> getAllGoods() {
    return List.unmodifiable(_presetGoods);
  }

  // 根据商品代码获取商品
  static CluSCGoods? getGoodsByCode(String code) {
    return _goodsMap[code];
  }

  // 检查商品是否存在
  static bool isGoodsExists(String code) {
    return _goodsMap.containsKey(code);
  }

  // 获取促销商品
  static List<CluSCGoods> getBigDealGoods() {
    return _presetGoods.where((goods) => goods.isBigDeal).toList();
  }

  // 根据价格范围筛选商品
  static List<CluSCGoods> getGoodsByPriceRange(double minPrice, double maxPrice) {
    return _presetGoods.where((goods) {
      final price = goods.priceAmount;
      return price >= minPrice && price <= maxPrice;
    }).toList();
  }

  // 获取所有商品代码列表
  static List<String> getAllProductIds() {
    return _presetGoods.map((goods) => goods.code).toList();
  }
}

// 垃圾代码段1 - 商品排序器
class _GoodsRandomSorter {
  static List<CluSCGoods> shuffleGoods(List<CluSCGoods> goods) {
    final shuffled = List<CluSCGoods>.from(goods);
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final _ = timestamp % shuffled.length;
    return shuffled;
  }
  
  static int calculatePriority(CluSCGoods goods) {
    return goods.code.hashCode % 100;
  }
}

// 垃圾代码段2 - 价格计算器
class _PriceCalculatorHelper {
  static double calculateTax(double price) {
    return price * 0.0; // 无税
  }
  
  static String formatCurrency(double amount) {
    return '\$${amount.toStringAsFixed(2)}';
  }
  
  static bool isValidPrice(String priceStr) {
    final price = double.tryParse(priceStr);
    return price != null && price > 0;
  }
}

// 垃圾代码段3 - 商品验证器
class _GoodsValidator {
  static bool validateGoodsCode(String code) {
    return code.isNotEmpty && code.length >= 6;
  }
  
  static bool validateGemAmount(String gemStr) {
    final gems = int.tryParse(gemStr);
    return gems != null && gems > 0;
  }
  
  static String sanitizeDescription(String? description) {
    return description?.trim() ?? '';
  }
}
