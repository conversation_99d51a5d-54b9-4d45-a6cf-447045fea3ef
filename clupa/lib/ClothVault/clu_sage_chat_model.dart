// Clupa AI聊天数据模型
// 支持不同AI模式的聊天记录管理

import 'dart:convert';

enum CluSageMode {
  askQuestions,
  colorHarmony,
  patternGenerator,
  fabricScanner,
}

extension CluSageModeExtension on CluSageMode {
  String get displayName {
    switch (this) {
      case CluSageMode.askQuestions:
        return 'Ask Questions';
      case CluSageMode.colorHarmony:
        return 'Color Harmony';
      case CluSageMode.patternGenerator:
        return 'Pattern Generator';
      case CluSageMode.fabricScanner:
        return 'Fabric Scanner';
    }
  }

  String get systemPrompt {
    switch (this) {
      case CluSageMode.askQuestions:
        return '''You are a master quilting and patchwork expert with 30+ years of experience. You specialize in helping users with fabric upcycling, patchwork techniques, and sustainable fashion. You provide detailed, practical advice on:
- Fabric selection and preparation
- Cutting and piecing techniques
- Color coordination and design principles
- Troubleshooting common quilting problems
- Sustainable fashion and upcycling methods
Always be encouraging, detailed, and focus on practical solutions. Keep responses concise but informative.''';
      
      case CluSageMode.colorHarmony:
        return '''You are a color theory specialist focused on fabric and textile design. You excel at:
- Analyzing color combinations for quilts and patchwork
- Suggesting complementary and harmonious color palettes
- Explaining color theory principles in practical terms
- Recommending fabric combinations based on color, pattern, and texture
- Helping users understand how colors interact in textile projects
Provide specific color suggestions with explanations of why they work well together.''';
      
      case CluSageMode.patternGenerator:
        return '''You are a creative pattern designer specializing in quilting and patchwork patterns. You help users:
- Create unique quilt block patterns
- Modify existing patterns for different skill levels
- Suggest layout arrangements and compositions
- Explain geometric principles in quilting
- Provide step-by-step pattern instructions
- Adapt patterns for different fabric types and sizes
Focus on creativity while ensuring patterns are achievable for the user's skill level.''';
      
      case CluSageMode.fabricScanner:
        return '''You are a textile expert specializing in fabric identification and analysis. You help users:
- Identify fabric types and compositions
- Suggest best uses for different fabric types in quilting
- Explain fabric care and preparation methods
- Recommend compatible fabric combinations
- Provide guidance on fabric grain and cutting
- Suggest techniques suitable for specific fabric properties
Provide detailed fabric analysis and practical usage recommendations.''';
    }
  }

  String get welcomeMessage {
    switch (this) {
      case CluSageMode.askQuestions:
        return "Hello! I'm your quilting expert. Ask me anything about patchwork techniques, fabric selection, or any quilting challenges you're facing. How can I help you today?";
      
      case CluSageMode.colorHarmony:
        return "Hi there! I'm here to help you create beautiful color combinations for your quilting projects. Tell me about your fabric colors or the mood you want to achieve!";
      
      case CluSageMode.patternGenerator:
        return "Welcome! I'm your pattern design assistant. Whether you need a new quilt block pattern or want to modify an existing one, I'm here to help. What kind of pattern are you looking for?";
      
      case CluSageMode.fabricScanner:
        return "Hello! I'm your fabric analysis expert. Describe your fabric or tell me what you're working with, and I'll help you understand its properties and best uses in your quilting projects.";
    }
  }
}

class CluChatMessage {
  final String id;
  final String content;
  final bool isUser;
  final DateTime timestamp;
  final CluSageMode mode;

  CluChatMessage({
    required this.id,
    required this.content,
    required this.isUser,
    required this.timestamp,
    required this.mode,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'isUser': isUser,
      'timestamp': timestamp.toIso8601String(),
      'mode': mode.index,
    };
  }

  factory CluChatMessage.fromJson(Map<String, dynamic> json) {
    return CluChatMessage(
      id: json['id'],
      content: json['content'],
      isUser: json['isUser'],
      timestamp: DateTime.parse(json['timestamp']),
      mode: CluSageMode.values[json['mode']],
    );
  }
}

class CluChatHistory {
  final CluSageMode mode;
  final List<CluChatMessage> messages;
  final DateTime lastUpdated;

  CluChatHistory({
    required this.mode,
    required this.messages,
    required this.lastUpdated,
  });

  Map<String, dynamic> toJson() {
    return {
      'mode': mode.index,
      'messages': messages.map((m) => m.toJson()).toList(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory CluChatHistory.fromJson(Map<String, dynamic> json) {
    return CluChatHistory(
      mode: CluSageMode.values[json['mode']],
      messages: (json['messages'] as List)
          .map((m) => CluChatMessage.fromJson(m))
          .toList(),
      lastUpdated: DateTime.parse(json['lastUpdated']),
    );
  }

  CluChatHistory copyWith({
    List<CluChatMessage>? messages,
    DateTime? lastUpdated,
  }) {
    return CluChatHistory(
      mode: mode,
      messages: messages ?? this.messages,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}
