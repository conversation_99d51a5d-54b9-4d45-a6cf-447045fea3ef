// Clupa布料档案模型 - TextileFolio
// 存储布料的材质、厚度、色牢度等属性信息

import 'package:meta/meta.dart';
import 'package:flutter/material.dart';

@immutable
class CluTextileFolio {
  final String textileId;
  final String name;
  final String description;
  final String? imageUrl;
  
  // 材质属性
  final String materialType; // "Cotton", "Linen", "Silk", "Wool", "Synthetic", etc.
  final String weaveType; // "Plain", "Twill", "Satin", "Knit", etc.
  final double weight; // 布料重量 (g/m²)
  final double thickness; // 厚度 (mm)
  
  // 颜色和图案
  final Color primaryColor;
  final Color? secondaryColor;
  final String colorName;
  final String patternType; // "Solid", "Floral", "Geometric", "Abstract", etc.
  final bool hasPattern;
  
  // 技术属性
  final double durability; // 耐用性评分 (0-10)
  final double colorFastness; // 色牢度 (0-10)
  final double shrinkageRate; // 缩水率 (%)
  final bool isWashable;
  final bool isDryCleanOnly;
  final bool isIronSafe;
  
  // 拼布适用性
  final double patchworkSuitability; // 拼布适用性评分 (0-10)
  final List<String> recommendedTechniques; // 推荐技法
  final List<String> compatibleMaterials; // 兼容材质
  final String difficultyLevel; // "Beginner", "Intermediate", "Advanced"
  
  // 来源信息
  final String source; // "Vintage", "New", "Upcycled", "Donated"
  final DateTime? acquisitionDate;
  final double? originalPrice;
  final String? brand;
  
  // 使用状态
  final bool isAvailable;
  final double remainingQuantity; // 剩余数量 (m²)
  final List<String> usedInCraftIds; // 已用于的作品ID列表

  const CluTextileFolio({
    required this.textileId,
    required this.name,
    required this.description,
    this.imageUrl,
    required this.materialType,
    required this.weaveType,
    required this.weight,
    required this.thickness,
    required this.primaryColor,
    this.secondaryColor,
    required this.colorName,
    required this.patternType,
    this.hasPattern = false,
    required this.durability,
    required this.colorFastness,
    required this.shrinkageRate,
    this.isWashable = true,
    this.isDryCleanOnly = false,
    this.isIronSafe = true,
    required this.patchworkSuitability,
    this.recommendedTechniques = const [],
    this.compatibleMaterials = const [],
    required this.difficultyLevel,
    required this.source,
    this.acquisitionDate,
    this.originalPrice,
    this.brand,
    this.isAvailable = true,
    required this.remainingQuantity,
    this.usedInCraftIds = const [],
  });

  // 从JSON创建实例
  factory CluTextileFolio.fromJson(Map<String, dynamic> json) {
    return CluTextileFolio(
      textileId: json['textile_id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      imageUrl: json['image_url'] as String?,
      materialType: json['material_type'] as String,
      weaveType: json['weave_type'] as String,
      weight: (json['weight'] as num).toDouble(),
      thickness: (json['thickness'] as num).toDouble(),
      primaryColor: Color(json['primary_color'] as int),
      secondaryColor: json['secondary_color'] != null 
          ? Color(json['secondary_color'] as int)
          : null,
      colorName: json['color_name'] as String,
      patternType: json['pattern_type'] as String,
      hasPattern: json['has_pattern'] as bool? ?? false,
      durability: (json['durability'] as num).toDouble(),
      colorFastness: (json['color_fastness'] as num).toDouble(),
      shrinkageRate: (json['shrinkage_rate'] as num).toDouble(),
      isWashable: json['is_washable'] as bool? ?? true,
      isDryCleanOnly: json['is_dry_clean_only'] as bool? ?? false,
      isIronSafe: json['is_iron_safe'] as bool? ?? true,
      patchworkSuitability: (json['patchwork_suitability'] as num).toDouble(),
      recommendedTechniques: List<String>.from(json['recommended_techniques'] ?? []),
      compatibleMaterials: List<String>.from(json['compatible_materials'] ?? []),
      difficultyLevel: json['difficulty_level'] as String,
      source: json['source'] as String,
      acquisitionDate: json['acquisition_date'] != null 
          ? DateTime.parse(json['acquisition_date'] as String)
          : null,
      originalPrice: json['original_price'] != null 
          ? (json['original_price'] as num).toDouble()
          : null,
      brand: json['brand'] as String?,
      isAvailable: json['is_available'] as bool? ?? true,
      remainingQuantity: (json['remaining_quantity'] as num).toDouble(),
      usedInCraftIds: List<String>.from(json['used_in_craft_ids'] ?? []),
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'textile_id': textileId,
      'name': name,
      'description': description,
      'image_url': imageUrl,
      'material_type': materialType,
      'weave_type': weaveType,
      'weight': weight,
      'thickness': thickness,
      'primary_color': primaryColor.value,
      'secondary_color': secondaryColor?.value,
      'color_name': colorName,
      'pattern_type': patternType,
      'has_pattern': hasPattern,
      'durability': durability,
      'color_fastness': colorFastness,
      'shrinkage_rate': shrinkageRate,
      'is_washable': isWashable,
      'is_dry_clean_only': isDryCleanOnly,
      'is_iron_safe': isIronSafe,
      'patchwork_suitability': patchworkSuitability,
      'recommended_techniques': recommendedTechniques,
      'compatible_materials': compatibleMaterials,
      'difficulty_level': difficultyLevel,
      'source': source,
      'acquisition_date': acquisitionDate?.toIso8601String(),
      'original_price': originalPrice,
      'brand': brand,
      'is_available': isAvailable,
      'remaining_quantity': remainingQuantity,
      'used_in_craft_ids': usedInCraftIds,
    };
  }

  // 复制并修改
  CluTextileFolio copyWith({
    String? textileId,
    String? name,
    String? description,
    String? imageUrl,
    String? materialType,
    String? weaveType,
    double? weight,
    double? thickness,
    Color? primaryColor,
    Color? secondaryColor,
    String? colorName,
    String? patternType,
    bool? hasPattern,
    double? durability,
    double? colorFastness,
    double? shrinkageRate,
    bool? isWashable,
    bool? isDryCleanOnly,
    bool? isIronSafe,
    double? patchworkSuitability,
    List<String>? recommendedTechniques,
    List<String>? compatibleMaterials,
    String? difficultyLevel,
    String? source,
    DateTime? acquisitionDate,
    double? originalPrice,
    String? brand,
    bool? isAvailable,
    double? remainingQuantity,
    List<String>? usedInCraftIds,
  }) {
    return CluTextileFolio(
      textileId: textileId ?? this.textileId,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      materialType: materialType ?? this.materialType,
      weaveType: weaveType ?? this.weaveType,
      weight: weight ?? this.weight,
      thickness: thickness ?? this.thickness,
      primaryColor: primaryColor ?? this.primaryColor,
      secondaryColor: secondaryColor ?? this.secondaryColor,
      colorName: colorName ?? this.colorName,
      patternType: patternType ?? this.patternType,
      hasPattern: hasPattern ?? this.hasPattern,
      durability: durability ?? this.durability,
      colorFastness: colorFastness ?? this.colorFastness,
      shrinkageRate: shrinkageRate ?? this.shrinkageRate,
      isWashable: isWashable ?? this.isWashable,
      isDryCleanOnly: isDryCleanOnly ?? this.isDryCleanOnly,
      isIronSafe: isIronSafe ?? this.isIronSafe,
      patchworkSuitability: patchworkSuitability ?? this.patchworkSuitability,
      recommendedTechniques: recommendedTechniques ?? this.recommendedTechniques,
      compatibleMaterials: compatibleMaterials ?? this.compatibleMaterials,
      difficultyLevel: difficultyLevel ?? this.difficultyLevel,
      source: source ?? this.source,
      acquisitionDate: acquisitionDate ?? this.acquisitionDate,
      originalPrice: originalPrice ?? this.originalPrice,
      brand: brand ?? this.brand,
      isAvailable: isAvailable ?? this.isAvailable,
      remainingQuantity: remainingQuantity ?? this.remainingQuantity,
      usedInCraftIds: usedInCraftIds ?? this.usedInCraftIds,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CluTextileFolio &&
          runtimeType == other.runtimeType &&
          textileId == other.textileId;

  @override
  int get hashCode => textileId.hashCode;

  @override
  String toString() {
    return 'CluTextileFolio{textileId: $textileId, name: $name, material: $materialType}';
  }
}
