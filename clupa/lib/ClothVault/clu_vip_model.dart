// Clupa用户模型 - VipProfile
// 避免使用user等常规命名，使用VIP体现奢华感

import 'package:meta/meta.dart';

@immutable
class CluVipProfile {
  final String vipId;
  final String displayName;
  final String email;
  final String? avatarUrl;
  final String? bio;
  
  // 创作等级系统
  final int craftLevel;
  final int totalCraftsPublished;
  final int totalLikesReceived;
  final int totalCommentsReceived;
  
  // 发布权限
  final int remainingPublishCount;
  final bool hasUnlimitedPublish;
  final DateTime? lastPublishDate;
  
  // 社交数据
  final int followersCount;
  final int followingCount;
  final List<String> followingVipIds;
  final List<String> followerVipIds;
  
  // 偏好设置
  final List<String> favoriteTextileTypes;
  final List<String> preferredCraftStyles;
  final bool enableNotifications;
  final bool enableSageAssistant;
  
  // 账户状态
  final DateTime createdAt;
  final DateTime lastActiveAt;
  final bool isVerified;
  final bool isPremiumVip;

  const CluVipProfile({
    required this.vipId,
    required this.displayName,
    required this.email,
    this.avatarUrl,
    this.bio,
    this.craftLevel = 1,
    this.totalCraftsPublished = 0,
    this.totalLikesReceived = 0,
    this.totalCommentsReceived = 0,
    this.remainingPublishCount = 3,
    this.hasUnlimitedPublish = false,
    this.lastPublishDate,
    this.followersCount = 0,
    this.followingCount = 0,
    this.followingVipIds = const [],
    this.followerVipIds = const [],
    this.favoriteTextileTypes = const [],
    this.preferredCraftStyles = const [],
    this.enableNotifications = true,
    this.enableSageAssistant = true,
    required this.createdAt,
    required this.lastActiveAt,
    this.isVerified = false,
    this.isPremiumVip = false,
  });

  // 从JSON创建实例
  factory CluVipProfile.fromJson(Map<String, dynamic> json) {
    return CluVipProfile(
      vipId: json['vip_id'] as String,
      displayName: json['display_name'] as String,
      email: json['email'] as String,
      avatarUrl: json['avatar_url'] as String?,
      bio: json['bio'] as String?,
      craftLevel: json['craft_level'] as int? ?? 1,
      totalCraftsPublished: json['total_crafts_published'] as int? ?? 0,
      totalLikesReceived: json['total_likes_received'] as int? ?? 0,
      totalCommentsReceived: json['total_comments_received'] as int? ?? 0,
      remainingPublishCount: json['remaining_publish_count'] as int? ?? 3,
      hasUnlimitedPublish: json['has_unlimited_publish'] as bool? ?? false,
      lastPublishDate: json['last_publish_date'] != null 
          ? DateTime.parse(json['last_publish_date'] as String)
          : null,
      followersCount: json['followers_count'] as int? ?? 0,
      followingCount: json['following_count'] as int? ?? 0,
      followingVipIds: List<String>.from(json['following_vip_ids'] ?? []),
      followerVipIds: List<String>.from(json['follower_vip_ids'] ?? []),
      favoriteTextileTypes: List<String>.from(json['favorite_textile_types'] ?? []),
      preferredCraftStyles: List<String>.from(json['preferred_craft_styles'] ?? []),
      enableNotifications: json['enable_notifications'] as bool? ?? true,
      enableSageAssistant: json['enable_sage_assistant'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastActiveAt: DateTime.parse(json['last_active_at'] as String),
      isVerified: json['is_verified'] as bool? ?? false,
      isPremiumVip: json['is_premium_vip'] as bool? ?? false,
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'vip_id': vipId,
      'display_name': displayName,
      'email': email,
      'avatar_url': avatarUrl,
      'bio': bio,
      'craft_level': craftLevel,
      'total_crafts_published': totalCraftsPublished,
      'total_likes_received': totalLikesReceived,
      'total_comments_received': totalCommentsReceived,
      'remaining_publish_count': remainingPublishCount,
      'has_unlimited_publish': hasUnlimitedPublish,
      'last_publish_date': lastPublishDate?.toIso8601String(),
      'followers_count': followersCount,
      'following_count': followingCount,
      'following_vip_ids': followingVipIds,
      'follower_vip_ids': followerVipIds,
      'favorite_textile_types': favoriteTextileTypes,
      'preferred_craft_styles': preferredCraftStyles,
      'enable_notifications': enableNotifications,
      'enable_sage_assistant': enableSageAssistant,
      'created_at': createdAt.toIso8601String(),
      'last_active_at': lastActiveAt.toIso8601String(),
      'is_verified': isVerified,
      'is_premium_vip': isPremiumVip,
    };
  }

  // 复制并修改
  CluVipProfile copyWith({
    String? vipId,
    String? displayName,
    String? email,
    String? avatarUrl,
    String? bio,
    int? craftLevel,
    int? totalCraftsPublished,
    int? totalLikesReceived,
    int? totalCommentsReceived,
    int? remainingPublishCount,
    bool? hasUnlimitedPublish,
    DateTime? lastPublishDate,
    int? followersCount,
    int? followingCount,
    List<String>? followingVipIds,
    List<String>? followerVipIds,
    List<String>? favoriteTextileTypes,
    List<String>? preferredCraftStyles,
    bool? enableNotifications,
    bool? enableSageAssistant,
    DateTime? createdAt,
    DateTime? lastActiveAt,
    bool? isVerified,
    bool? isPremiumVip,
  }) {
    return CluVipProfile(
      vipId: vipId ?? this.vipId,
      displayName: displayName ?? this.displayName,
      email: email ?? this.email,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      bio: bio ?? this.bio,
      craftLevel: craftLevel ?? this.craftLevel,
      totalCraftsPublished: totalCraftsPublished ?? this.totalCraftsPublished,
      totalLikesReceived: totalLikesReceived ?? this.totalLikesReceived,
      totalCommentsReceived: totalCommentsReceived ?? this.totalCommentsReceived,
      remainingPublishCount: remainingPublishCount ?? this.remainingPublishCount,
      hasUnlimitedPublish: hasUnlimitedPublish ?? this.hasUnlimitedPublish,
      lastPublishDate: lastPublishDate ?? this.lastPublishDate,
      followersCount: followersCount ?? this.followersCount,
      followingCount: followingCount ?? this.followingCount,
      followingVipIds: followingVipIds ?? this.followingVipIds,
      followerVipIds: followerVipIds ?? this.followerVipIds,
      favoriteTextileTypes: favoriteTextileTypes ?? this.favoriteTextileTypes,
      preferredCraftStyles: preferredCraftStyles ?? this.preferredCraftStyles,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      enableSageAssistant: enableSageAssistant ?? this.enableSageAssistant,
      createdAt: createdAt ?? this.createdAt,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
      isVerified: isVerified ?? this.isVerified,
      isPremiumVip: isPremiumVip ?? this.isPremiumVip,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CluVipProfile &&
          runtimeType == other.runtimeType &&
          vipId == other.vipId;

  @override
  int get hashCode => vipId.hashCode;

  @override
  String toString() {
    return 'CluVipProfile{vipId: $vipId, displayName: $displayName, craftLevel: $craftLevel}';
  }
}
