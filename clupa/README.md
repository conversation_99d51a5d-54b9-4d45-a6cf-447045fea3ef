# Clupa - 旧衣布料改造拼布AI指导、分享社区

## 项目概述

Clupa 是一款结合了创作、分享与AI指导的社区平台，专注于旧衣布料改造与拼布设计。用户不仅可以获得个性化的AI指导，还能通过平台展示、分享自己的拼布作品。无论是花色搭配、布局设计还是针法选择，Clupa 的AI助手都为用户提供全面的支持，同时平台鼓励用户通过视频展示自己的创作，与社区其他成员互动与交流。

## 目标用户

- **拼布初学者**：希望学习基础拼布技巧和布料搭配的用户
- **服装改造者**：关注环保和个性化设计的DIY达人
- **手工艺创作者**：需要创意灵感和设计工具支持的拼布艺术家
- **环保倡导者**：喜爱旧物改造和可持续时尚的用户
- **创作者社区爱好者**：喜欢分享创作并与他人互动的用户

## 核心功能

### 2.1 作品发布与分享
用户可以通过Clupa上传并展示自己的拼布视频作品，获得社区点赞与评论。每个用户可以免费发布 3 次作品，超出次数后需要内购解锁更多发布次数。
- **视频展示**：分享创作过程与完成作品，通过视频记录拼布的每一步，激发其他用户的创作灵感

### 2.2 AI指导与建议
Clupa 提供实时的AI创作指导，用户可以与AI助手对话，获取关于花色匹配、布局设计和针法选择等方面的建议。AI助手还可以帮助用户修正不合适的部分，确保每个作品都能达到最佳效果。

### 2.3 创作灵感与设计调整
Clupa 不仅提供智能建议，还能够根据用户的需求，快速调整设计，提供流行的拼布风格和创意工艺案例，激发创作灵感。

## 内购设计

### 功能性内购点
- **作品发布次数包**：增加作品发布次数，提升社区曝光度

## UI设计色彩方案

### 主色调 - 复古温馨
- **主色调**：Warm Taupe (#BDAE9E) - 温暖的褐灰色，营造复古、舒适的氛围
- **辅助色调**：Dusty Rose (#CFA9A2) - 带有粉调的土色，适用于按钮与交互元素
- **强调色1**：Deep Denim (#3B506C) - 经典牛仔蓝，用于标题和重要信息
- **强调色2**：Olive Drab (#7A8450) - 自然的橄榄绿，适用于辅助文本与背景装饰
- **文字辅助色**：Vintage White (#F7F5F1) - 柔和米白色，保证长时间阅读舒适

## 商店信息

**APP名称**：Clupa
**关键词**：Fabric, Patchwork, AI, Design, Sustainability, Community, Share
**商店描述**：Clupa is your AI-powered patchwork guide and community platform! Whether you're a beginner or a seasoned crafter, Clupa helps you design beautiful patchwork projects and share them with a vibrant community. Get personalized advice from our AI assistants, connect with other creators, and showcase your work through video sharing. Join Clupa and turn your fabric scraps into art, while inspiring others along the way!

## 技术架构

### 状态管理 + 设计模式：Redux + MVC

### 核心目录架构（拼布主题化命名）

#### 🧵 核心架构目录（Redux + MVC 分层）

1. **数据实体层 → ClothVault**
   - 含义：Cloth（布料） + Vault（资源库），替代"Models"，存储旧衣属性、拼布方案等核心数据
   - 内容：
     - 布料档案 TextileFolio（材质/厚度/色牢度）
     - 拼布蓝图 PatchBlueprint（几何拼接规则库）

2. **界面交互层 → LoomView**
   - 含义：Loom（织布机） + View（视图），替代"Screens"，隐喻拼布创作的可视化织机
   - 内容：
     - 改造工作台 RemakeLoom（实时拼布设计界面）
     - 社区展厅 PatchGallery（用户作品3D展示墙）

3. **业务协调层 → StitchMaster**
   - 含义：Stitch（针脚） + Master（指导师），替代"Controllers"，管理拼布逻辑与AI协作
   - Redux 整合：
     - Action 定义 → **NeedleActions**（裁剪/缝合等操作）
     - Reducer 逻辑 → **WeaveReducer**（响应操作更新拼布状态）

4. **状态管理层 → ThreadStore**
   - 含义：Thread（线） + Store（存储），替代"State"，Redux 核心状态容器
   - 结构：
     - 布料状态流 TextileStream（当前旧衣材质/颜色）
     - 拼布状态库 PatchVault（用户草稿与方案进度）

#### ✂️ 扩展功能目录（拼布场景化命名）

5. **AI指导引擎 → PatternSage**
   - 含义：Pattern（图案） + Sage（智者），封装AI拼布推荐算法
   - 内容：
     - 拼布生成器 QuiltWhisper（几何图案智能生成）
     - 兼容校验器 HarmonyCheck（检测布料色系/纹理适配性）

6. **社交协作层 → PatchSquare**
   - 含义：Patch（布片） + Square（广场），支持用户作品共享与协作
   - 内容：
     - 实时拼布室 LiveStitchRoom（多人协同设计）
     - 灵感布告墙 InspireBoard（用户改造方案共享）

7. **改造知识库 → SalvageArchive**
   - 含义：Salvage（废料利用） + Archive（档案库），存储拼布技法与案例
   - 内容：
     - 技法手册 StitchFolio（嵌花/贴布绣等教程）
     - 再生案例库 ReclaimCase（旧衣改造高赞方案）

#### 🧩 支持性目录

8. **UI 组件库 → FiberParts**
   - 含义：Fiber（纤维） + Parts（零件），提供纺织风格组件
   - 组件例：
     - 布料选择器 TextilePicker（带材质预览的3D选择器）
     - 拼布模拟器 PatchSimulator（实时渲染拼布效果）

9. **配置与路由 → WeaveRoutes**
   - 含义：Weave（编织） + Routes（路径），定义导航流与主题
   - 内容：
     - 创作流 CraftTrail（选布→拆解→拼合→发布）
     - 主题色库 EcoPalette（靛蓝 #4B0082、再生绿 #2E8B57）

## 页面结构规划

基于产品需求，应用需要以下核心页面/视图结构：

| 页面名称 | 功能描述 | 优先级 | 技术要点 |
|---------|---------|--------|---------|
| **启动引导页** | 应用介绍、权限申请、用户引导 | 高 | 动画引导、权限管理 |
| **主工作台页** | 拼布设计、AI指导、实时预览 | 高 | AI集成、实时渲染 |
| **社区广场页** | 作品展示、点赞评论、用户互动 | 高 | 视频播放、社交功能 |
| **AI助手对话页** | 智能问答、设计建议、技法指导 | 高 | 对话界面、AI集成 |
| **作品发布页** | 视频上传、作品描述、标签设置 | 中 | 视频处理、表单验证 |
| **个人创作档案页** | 作品管理、创作历史、成就展示 | 中 | 数据展示、状态管理 |
| **拼布技法库页** | 教程浏览、技法学习、案例参考 | 中 | 内容展示、搜索功能 |
| **布料识别页** | 拍照识别、材质分析、属性记录 | 中 | 相机集成、图像识别 |
| **设置配置页** | 账户设置、通知管理、隐私设置 | 低 | 表单处理、数据持久化 |
| **内购商店页** | 发布次数包、高级功能解锁 | 低 | 内购集成、支付处理 |

## 数据模型设计

### 核心数据实体

1. **用户模型 (VipProfile)**
   - 用户基础信息、创作等级、发布次数余额

2. **作品模型 (CraftPiece)**
   - 视频文件、描述信息、点赞数、评论列表

3. **布料模型 (TextileFolio)**
   - 材质属性、颜色信息、厚度数据、适配性评分

4. **拼布方案模型 (PatchBlueprint)**
   - 设计图案、几何布局、针法推荐、难度等级

5. **AI对话模型 (SageChat)**
   - 对话历史、建议记录、上下文状态

## 技术实现细节

*此部分将随着开发过程逐步添加各页面的技术方案*

## 开发状态跟踪

| 页面/组件名称 | 开发状态 | 文件路径 | 完成度 |
|-------------|---------|---------|--------|
| 启动引导页 | ⏳ 待开发 | - | 0% |
| 主工作台页 | ⏳ 待开发 | - | 0% |
| 社区广场页 | ⏳ 待开发 | - | 0% |
| AI助手对话页 | ⏳ 待开发 | - | 0% |
| 作品发布页 | ⏳ 待开发 | - | 0% |
| 个人创作档案页 | ⏳ 待开发 | - | 0% |
| 拼布技法库页 | ⏳ 待开发 | - | 0% |
| 布料识别页 | ⏳ 待开发 | - | 0% |
| 设置配置页 | ⏳ 待开发 | - | 0% |
| 内购商店页 | ⏳ 待开发 | - | 0% |

## 特色与亮点

- **AI助手引导**：提供从花色搭配到针法推荐的全程指导，确保用户每一步都轻松掌握
- **作品发布与分享**：通过视频展示和社区互动，用户可以展示自己的创作，与他人分享经验与灵感
- **创作灵感**：AI为用户提供最新的拼布风格和工艺案例，激发创作热情
- **社区互动**：用户可以点赞和评论他人的作品，增加互动与社区参与感
- **环保理念**：专注旧衣改造，倡导可持续时尚和环保创作

## 开发规范与要求

### 🚨 关键开发规则（必须严格遵守）

#### 1. 命名差异化要求
**严禁使用常规命名**，必须使用差异化命名避免审核问题：

**禁用词汇列表**：
- 主页：home ❌ → 使用 `CluMainHub` ✅
- 聊天历史：history ❌ → 使用 `CluChatArchive` ✅
- 个人中心：profile ❌ → 使用 `CluVipZone` ✅
- 底部导航：bottom/navigation ❌ → 使用 `CluFooterTabs` ✅
- 反馈：feedback ❌ → 使用 `CluSuggestionBox` ✅
- 商店：shop/store ❌ → 使用 `CluMarketPlace` ✅
- 购买：purchase/buy ❌ → 使用 `CluAcquire` ✅
- 帖子详情：post/detail ❌ → 使用 `CluContentView` ✅
- 金币：coin ❌ → 使用 `CluCredits` ✅
- 用户：user ❌ → 使用 `CluVip` ✅

**命名规则**：
- 文件名前缀：`Clu` + 差异化名称
- 类名：`Clu` + 功能描述 + 随机数字/字母
- 变量名：避免通用词汇，使用拼音/首字母/创意组合
- 目录名：按产品文档的拼布主题化命名

#### 2. 语言使用规范
- ✅ **代码注释**：可使用中文
- ✅ **控制台输出**：可使用中文
- ❌ **APP显示内容**：严禁中文，必须英文
- ❌ **权限设置描述**：严禁中文（审核雷区）

#### 3. 技术架构要求
- **状态管理**：Redux + MVC设计模式
- **动画效果**：使用高级动画库
  - Lottie动画
  - Flutter Animate
  - Simple Animations
  - 禁用低端画板动画

#### 4. 资源管理要求
- **字体**：联网搜索贴合拼布主题的字体，下载到本地
- **图片**：使用Unsplash API下载，存储到本地文件夹
- **视频**：已提供video1.mp4-video3.mp4，预设6-8组数据复用

### 📁 预设资源信息

#### 主页视频流资源
- 位置：`assets/` 文件夹
- 文件：`video1.mp4`, `video2.mp4`, `video3.mp4`
- 要求：预设6-8组视频数据，复用这3个视频地址

#### Unsplash API配置
```
Application ID: 767748
Access Key: sB7G-rHWgjtvcp1Ag78GATMnmDApT_WACkbWdygsgKM
Secret Key: b-RsiPe7hsfsVDneyWel1503jBiaKFBMqCq5dUTWvAY
```

#### AI聊天API配置
```bash
curl https://api.moonshot.cn/v1/chat/completions \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer sk-UjLDXgyVsdh4kNtrLbbu1yFFhuafggJTxEw03ezpnnvXX2fR"\
    -d '{
        "model": "moonshot-v1-8k",
        "messages": [
            {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手..."},
            {"role": "user", "content": "用户输入内容"}
        ],
        "temperature": 0.3
   }'
```

### 🎨 UI设计要求
- **主页形式**：竖屏视频流（类似抖音/TikTok）
- **视觉风格**：复古温馨拼布主题
- **交互体验**：高级动画效果，避免粗糙实现
- **用户头像**：通过Python脚本+Unsplash API自动下载

### ✅ 资源准备状态
- **视频资源**：✅ 已准备（video1.mp4 - video3.mp4）
- **拼布图片**：✅ 已下载（15张拼布相关图片）
- **纹理背景**：✅ 已下载（10张纹理图片）
- **用户头像**：✅ 已配置（8个头像占位符）
- **资源清单**：✅ 已生成（asset_manifest.json）

---

*项目文档最后更新：2025-07-30*
