# Clupa - 旧衣布料改造拼布AI指导、分享社区

## 项目概述

Clupa 是一款结合了创作、分享与AI指导的社区平台，专注于旧衣布料改造与拼布设计。用户可以观看社区创作视频、发布自己的拼布作品、与AI助手对话获取专业指导，并通过金币系统解锁更多发布机会。

## 目标用户

- **拼布初学者**：希望学习基础拼布技巧和布料搭配的用户
- **服装改造者**：关注环保和个性化设计的DIY达人
- **手工艺创作者**：需要创意灵感和设计工具支持的拼布艺术家
- **环保倡导者**：喜爱旧物改造和可持续时尚的用户
- **创作者社区爱好者**：喜欢分享创作并与他人互动的用户

## 已实现功能详细说明

### 1. 视频社区功能
#### 1.1 视频流浏览
- **竖屏全屏播放**：支持上下滑动切换视频，自动播放和暂停
- **视频交互**：点赞、全屏播放、更多选项、创作者资料查看
- **视频来源**：包含预设的拼布教学视频和用户发布的作品
- **自动循环播放**：视频播放完毕后自动重新开始

#### 1.2 视频管理
- **屏蔽功能**：用户可以屏蔽不喜欢的视频内容
- **举报功能**：支持举报不当内容，24小时内处理
- **屏蔽管理**：在个人档案页面查看和管理已屏蔽的内容
- **缩略图展示**：屏蔽管理页面显示视频缩略图便于识别

### 2. 作品发布系统
#### 2.1 视频发布
- **视频上传**：支持从相册选择视频文件进行发布
- **内容编辑**：添加标题、描述和分类标签
- **分类系统**：支持拼布、缝纫、改造、教程、灵感、展示等分类
- **发布配额**：每日3次免费发布机会，超出需消费金币

#### 2.2 我的作品管理
- **作品列表**：查看所有已发布的个人作品
- **缩略图预览**：自动生成视频缩略图作为封面
- **作品详情**：显示标题、描述、点赞数、发布时间等信息
- **全屏播放**：点击作品可进入全屏播放模式
- **作品删除**：支持删除自己发布的作品

### 3. AI助手系统
#### 3.1 智能对话
- **多种模式**：问答咨询、色彩搭配、图案生成三种AI助手模式
- **专业指导**：针对拼布技巧、材料选择、图案设计提供专业建议
- **对话记录**：保存聊天历史，支持上下文连续对话
- **实时响应**：基于Moonshot AI提供快速智能回复

#### 3.2 专业建议
- **技巧指导**：针对拼布技法、针法选择提供详细指导
- **色彩搭配**：帮助用户选择和谐的颜色组合
- **图案设计**：协助生成独特的拼布图案和布局

### 4. 金币系统
#### 4.1 金币获取
- **内购商城**：提供多种金币套餐，支持iOS内购
- **安全支付**：集成Apple Store Kit，确保交易安全
- **即时到账**：购买成功后金币立即到账

#### 4.2 金币消费
- **视频发布**：免费次数用完后，50金币可发布一次视频
- **消费记录**：详细记录每次金币消费和获取历史
- **余额管理**：实时显示当前金币余额

### 5. 个人档案系统
#### 5.1 用户信息
- **头像设置**：支持选择和更换个人头像
- **昵称编辑**：自定义用户昵称
- **金币余额**：实时显示当前金币数量

#### 5.2 功能入口
- **我的作品**：查看和管理个人发布的所有视频
- **屏蔽管理**：管理已屏蔽和举报的内容
- **意见反馈**：向开发团队提交建议和反馈
- **隐私政策**：查看应用隐私条款

### 6. 点赞系统
#### 6.1 点赞功能
- **点赞切换**：支持点赞和取消点赞操作
- **本地持久化**：点赞状态本地保存，重启应用后保持
- **实时更新**：点赞数实时更新显示
- **用户作品支持**：自己发布的作品也支持点赞功能

### 7. 缩略图系统
#### 7.1 自动生成
- **视频缩略图**：自动为所有视频生成缩略图
- **智能缓存**：首次生成后本地缓存，提高加载速度
- **多格式支持**：支持Asset视频和本地视频文件
- **错误处理**：生成失败时显示默认占位图

### 8. 数据管理
#### 8.1 本地存储
- **用户配置**：头像、昵称等个人信息本地保存
- **发布记录**：用户发布的作品本地存储管理
- **点赞记录**：点赞状态和历史记录本地持久化
- **屏蔽列表**：屏蔽的内容ID本地保存

#### 8.2 状态管理
- **Redux架构**：使用Redux管理全局应用状态
- **实时同步**：各模块状态实时同步更新
- **数据一致性**：确保UI和数据状态保持一致

## 技术特色

### 金币经济系统
- **发布配额管理**：每日免费次数自动重置
- **金币消费机制**：50金币兑换一次发布机会
- **购买引导**：金币不足时智能引导用户前往商城

### 视频处理技术
- **多格式支持**：支持Asset资源和本地文件播放
- **自动适配**：根据视频路径类型自动选择播放器
- **缩略图生成**：自动提取视频第1秒帧作为缩略图
- **错误恢复**：播放失败时自动降级到默认视频

### 用户体验优化
- **触觉反馈**：关键操作提供触觉反馈增强体验
- **加载状态**：所有异步操作提供加载状态提示
- **错误处理**：完善的错误提示和恢复机制
- **界面适配**：支持不同屏幕尺寸的iPhone设备

## UI设计色彩方案

### 主色调 - 复古温馨
- **主色调**：Warm Taupe (#BDAE9E) - 温暖的褐灰色，营造复古、舒适的氛围
- **辅助色调**：Dusty Rose (#CFA9A2) - 带有粉调的土色，适用于按钮与交互元素
- **强调色1**：Deep Denim (#3B506C) - 经典牛仔蓝，用于标题和重要信息
- **强调色2**：Olive Drab (#7A8450) - 自然的橄榄绿，适用于辅助文本与背景装饰
- **文字辅助色**：Vintage White (#F7F5F1) - 柔和米白色，保证长时间阅读舒适

## 应用信息

**APP名称**：Clupa
**平台支持**：iOS (iPhone)
**开发语言**：Flutter/Dart
**最低系统要求**：iOS 12.0+
**版本**：1.0.0
**更新日期**：2025年8月1日

**关键词**：Fabric, Patchwork, AI, Design, Sustainability, Community, Share, Video
**商店描述**：Clupa is your AI-powered patchwork guide and community platform! Watch inspiring videos, share your own creations, and get personalized advice from our AI assistants. Join a vibrant community of creators and turn your fabric scraps into art while inspiring others along the way!

## 技术架构

### 状态管理 + 设计模式：Redux + MVC

### 核心目录架构（拼布主题化命名）

#### 🧵 核心架构目录（Redux + MVC 分层）

1. **数据实体层 → ClothVault**
   - 含义：Cloth（布料） + Vault（资源库），替代"Models"，存储旧衣属性、拼布方案等核心数据
   - 内容：
     - 布料档案 TextileFolio（材质/厚度/色牢度）
     - 拼布蓝图 PatchBlueprint（几何拼接规则库）

2. **界面交互层 → LoomView**
   - 含义：Loom（织布机） + View（视图），替代"Screens"，隐喻拼布创作的可视化织机
   - 内容：
     - 改造工作台 RemakeLoom（实时拼布设计界面）
     - 社区展厅 PatchGallery（用户作品3D展示墙）

3. **业务协调层 → StitchMaster**
   - 含义：Stitch（针脚） + Master（指导师），替代"Controllers"，管理拼布逻辑与AI协作
   - Redux 整合：
     - Action 定义 → **NeedleActions**（裁剪/缝合等操作）
     - Reducer 逻辑 → **WeaveReducer**（响应操作更新拼布状态）

4. **状态管理层 → ThreadStore**
   - 含义：Thread（线） + Store（存储），替代"State"，Redux 核心状态容器
   - 结构：
     - 布料状态流 TextileStream（当前旧衣材质/颜色）
     - 拼布状态库 PatchVault（用户草稿与方案进度）

#### ✂️ 扩展功能目录（拼布场景化命名）

5. **AI指导引擎 → PatternSage**
   - 含义：Pattern（图案） + Sage（智者），封装AI拼布推荐算法
   - 内容：
     - 拼布生成器 QuiltWhisper（几何图案智能生成）
     - 兼容校验器 HarmonyCheck（检测布料色系/纹理适配性）

6. **社交协作层 → PatchSquare**
   - 含义：Patch（布片） + Square（广场），支持用户作品共享与协作
   - 内容：
     - 实时拼布室 LiveStitchRoom（多人协同设计）
     - 灵感布告墙 InspireBoard（用户改造方案共享）

7. **改造知识库 → SalvageArchive**
   - 含义：Salvage（废料利用） + Archive（档案库），存储拼布技法与案例
   - 内容：
     - 技法手册 StitchFolio（嵌花/贴布绣等教程）
     - 再生案例库 ReclaimCase（旧衣改造高赞方案）

#### 🧩 支持性目录

8. **UI 组件库 → FiberParts**
   - 含义：Fiber（纤维） + Parts（零件），提供纺织风格组件
   - 组件例：
     - 布料选择器 TextilePicker（带材质预览的3D选择器）
     - 拼布模拟器 PatchSimulator（实时渲染拼布效果）

9. **配置与路由 → WeaveRoutes**
   - 含义：Weave（编织） + Routes（路径），定义导航流与主题
   - 内容：
     - 创作流 CraftTrail（选布→拆解→拼合→发布）
     - 主题色库 EcoPalette（靛蓝 #4B0082、再生绿 #2E8B57）

## 使用指南

### 首次使用
1. 下载并安装Clupa应用
2. 打开应用，浏览社区视频内容
3. 上下滑动切换不同的拼布创作视频
4. 点击右侧按钮进行点赞、全屏播放等操作

### 发布作品
1. 点击底部导航栏的"+"按钮进入发布页面
2. 选择视频文件（从相册选择）
3. 填写作品标题和描述
4. 选择合适的分类标签
5. 点击发布按钮（每日3次免费机会）

### AI助手使用
1. 点击底部导航栏的AI助手图标
2. 选择需要的服务类型：
   - 问答咨询：获取拼布技巧指导
   - 色彩搭配：获取颜色组合建议
   - 图案生成：创建独特的拼布图案
3. 输入问题或需求，获取AI专业建议

### 金币系统
1. 免费发布次数用完后，可使用金币继续发布
2. 点击个人档案页面的金币余额卡片进入商城
3. 选择合适的金币套餐进行购买
4. 50金币可兑换1次视频发布机会

### 个人管理
1. 在个人档案页面查看金币余额和个人信息
2. 点击"我的作品"查看已发布的所有视频
3. 点击"屏蔽管理"查看和管理已屏蔽的内容
4. 支持修改头像和昵称

## 联系我们

**技术支持邮箱**：<EMAIL>
**意见反馈**：应用内个人档案页面 → 意见反馈
**更新日期**：2025年8月1日

## 项目特色

- **AI智能指导**：集成Moonshot AI，提供专业的拼布技巧、色彩搭配和图案设计建议
- **视频社区平台**：竖屏视频流设计，支持作品展示、点赞互动和内容管理
- **金币经济系统**：创新的发布配额机制，每日免费次数+金币付费模式
- **完整的内容管理**：支持屏蔽、举报、删除等多种内容管理功能
- **本地数据持久化**：用户数据、点赞记录、发布作品等本地安全存储
- **iOS原生体验**：遵循Apple HIG设计规范，提供流畅的iOS用户体验

---

**项目完成日期**：2025年8月1日
**技术支持**：<EMAIL>
