#!/usr/bin/env python3
"""
Clupa资源下载脚本
用于从Unsplash下载用户头像和拼布相关图片
"""

import requests
import os
import json
from urllib.parse import urlparse
import time

# Unsplash API配置
UNSPLASH_ACCESS_KEY = "sB7G-rHWgjtvcp1Ag78GATMnmDApT_WACkbWdygsgKM"
UNSPLASH_BASE_URL = "https://api.unsplash.com"

# 本地存储路径
ASSETS_DIR = "../assets"
AVATARS_DIR = f"{ASSETS_DIR}/avatars"
PATCHWORK_DIR = f"{ASSETS_DIR}/patchwork"
TEXTURES_DIR = f"{ASSETS_DIR}/textures"

def create_directories():
    """创建必要的目录"""
    directories = [ASSETS_DIR, AVATARS_DIR, PATCHWORK_DIR, TEXTURES_DIR]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ 创建目录: {directory}")

def download_image(url, filepath):
    """下载图片到指定路径"""
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(filepath, 'wb') as file:
            for chunk in response.iter_content(chunk_size=8192):
                file.write(chunk)
        
        print(f"✅ 下载成功: {filepath}")
        return True
    except Exception as e:
        print(f"❌ 下载失败: {filepath} - {e}")
        return False

def search_unsplash_photos(query, count=10, orientation="portrait"):
    """搜索Unsplash图片"""
    headers = {"Authorization": f"Client-ID {UNSPLASH_ACCESS_KEY}"}
    params = {
        "query": query,
        "per_page": count,
        "orientation": orientation
    }
    
    try:
        response = requests.get(f"{UNSPLASH_BASE_URL}/search/photos", 
                              headers=headers, params=params)
        response.raise_for_status()
        return response.json()["results"]
    except Exception as e:
        print(f"❌ 搜索失败: {query} - {e}")
        return []

def download_avatar_images():
    """下载用户头像图片"""
    print("🔄 开始下载用户头像...")
    
    # 搜索人像照片作为头像
    queries = ["portrait", "face", "person", "people", "avatar"]
    
    avatar_count = 0
    for query in queries:
        if avatar_count >= 20:  # 下载20个头像
            break
            
        photos = search_unsplash_photos(query, count=5, orientation="square")
        
        for photo in photos:
            if avatar_count >= 20:
                break
                
            # 获取中等尺寸的图片URL
            image_url = photo["urls"]["regular"]
            filename = f"clu_avatar_{avatar_count + 1:02d}.jpg"
            filepath = f"{AVATARS_DIR}/{filename}"
            
            if download_image(image_url, filepath):
                avatar_count += 1
                time.sleep(0.5)  # 避免请求过快

def download_patchwork_images():
    """下载拼布相关图片"""
    print("🔄 开始下载拼布图片...")
    
    # 拼布相关关键词
    queries = [
        "patchwork", "quilting", "fabric", "textile", 
        "sewing", "handcraft", "vintage fabric", "quilt pattern"
    ]
    
    patchwork_count = 0
    for query in queries:
        if patchwork_count >= 15:  # 下载15张拼布图片
            break
            
        photos = search_unsplash_photos(query, count=3)
        
        for photo in photos:
            if patchwork_count >= 15:
                break
                
            image_url = photo["urls"]["regular"]
            filename = f"clu_patchwork_{patchwork_count + 1:02d}.jpg"
            filepath = f"{PATCHWORK_DIR}/{filename}"
            
            if download_image(image_url, filepath):
                patchwork_count += 1
                time.sleep(0.5)

def download_texture_images():
    """下载纹理背景图片"""
    print("🔄 开始下载纹理图片...")
    
    # 纹理相关关键词
    queries = [
        "fabric texture", "linen texture", "cotton texture",
        "vintage paper", "old fabric", "textile pattern"
    ]
    
    texture_count = 0
    for query in queries:
        if texture_count >= 10:  # 下载10张纹理图片
            break
            
        photos = search_unsplash_photos(query, count=2)
        
        for photo in photos:
            if texture_count >= 10:
                break
                
            image_url = photo["urls"]["regular"]
            filename = f"clu_texture_{texture_count + 1:02d}.jpg"
            filepath = f"{TEXTURES_DIR}/{filename}"
            
            if download_image(image_url, filepath):
                texture_count += 1
                time.sleep(0.5)

def generate_asset_manifest():
    """生成资源清单文件"""
    manifest = {
        "avatars": [],
        "patchwork": [],
        "textures": [],
        "videos": ["assets/av/video1.mp4", "assets/av/video2.mp4", "assets/av/video3.mp4"]
    }
    
    # 扫描下载的文件
    for filename in os.listdir(AVATARS_DIR):
        if filename.endswith(('.jpg', '.png')):
            manifest["avatars"].append(f"assets/avatars/{filename}")
    
    for filename in os.listdir(PATCHWORK_DIR):
        if filename.endswith(('.jpg', '.png')):
            manifest["patchwork"].append(f"assets/patchwork/{filename}")
    
    for filename in os.listdir(TEXTURES_DIR):
        if filename.endswith(('.jpg', '.png')):
            manifest["textures"].append(f"assets/textures/{filename}")
    
    # 保存清单文件
    with open(f"{ASSETS_DIR}/asset_manifest.json", 'w') as f:
        json.dump(manifest, f, indent=2)
    
    print("✅ 资源清单已生成: assets/asset_manifest.json")

def main():
    """主函数"""
    print("🚀 开始下载Clupa项目资源...")
    
    # 创建目录
    create_directories()
    
    # 下载各类图片
    download_avatar_images()
    download_patchwork_images()
    download_texture_images()
    
    # 生成资源清单
    generate_asset_manifest()
    
    print("🎉 所有资源下载完成！")

if __name__ == "__main__":
    main()
